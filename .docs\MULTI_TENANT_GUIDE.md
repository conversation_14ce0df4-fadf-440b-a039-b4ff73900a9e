# Multi-Tenant Architecture Documentation

## Overview
This application supports multiple hotels (tenants) from a single codebase deployment. Each hotel gets their own subdomain and isolated data.

## How It Works

### URL Structure
```
demo.hospitalityflows.com     → Grand Plaza Hotel (demo tenant)
parkhotel.hospitalityflows.com → Park Hotel & Spa
oceanview.hospitalityflows.com → Ocean View Resort
services.parkhotel.com        → Custom domain for Park Hotel
```

### Tenant Detection
The system automatically detects which hotel to display based on:
1. **Subdomain**: `parkhotel.hospitalityflows.com`
2. **Custom Domain**: `services.parkhotel.com`
3. **URL Parameter** (dev only): `localhost:3000?tenant=parkhotel`
4. **Default**: Falls back to demo tenant

### Data Isolation
Every database table includes a `tenant_id` column:
- `guest_announcements`
- `guest_requests`
- `staff_notifications`
- etc.

Queries automatically filter by the current tenant's ID.

## Adding a New Hotel

### 1. Update Tenant Configuration
Edit `src/lib/tenant-config.ts`:

```typescript
mynewhotel: {
  id: 'unique-uuid-here',
  name: 'My New Hotel',
  subdomain: 'mynewhotel',
  customDomain: 'portal.mynewhotel.com', // optional
  branding: {
    primaryColor: '#color',
    secondaryColor: '#color',
    portalTitle: 'Welcome to My New Hotel',
    portalSubtitle: 'Your tagline here'
  },
  settings: {
    timezone: 'America/New_York',
    currency: 'USD',
    language: 'en',
    features: {
      maintenance: true,
      housekeeping: true,
      concierge: true,
      announcements: true
    }
  },
  contact: {
    phone: '+****************',
    email: '<EMAIL>',
    address: '123 Hotel Street, City, ST 12345'
  }
}
```

### 2. Configure DNS
Add a CNAME record:
```
mynewhotel.hospitalityflows.com → hospitalityflows.com
```

Or for custom domain:
```
portal.mynewhotel.com → hospitalityflows.com
```

### 3. Deploy
The app automatically handles the new tenant!

## Testing in Development

### URL Parameters
Test different tenants locally:
- `http://localhost:3000?tenant=demo`
- `http://localhost:3000?tenant=parkhotel`
- `http://localhost:3000?tenant=oceanview`

### Tenant Debugger
In development mode, a debug panel shows in the bottom-right corner with:
- Current tenant info
- Quick links to switch tenants

## Feature Toggles
Control which services each hotel can access:

```typescript
features: {
  maintenance: false,    // Hide maintenance requests
  housekeeping: true,    // Show housekeeping
  concierge: true,      // Show concierge
  announcements: false  // Hide announcements
}
```

## Database Setup

### Required Tables
All tables must include:
```sql
tenant_id UUID NOT NULL
```

### Row Level Security (RLS)
Example policy:
```sql
CREATE POLICY "Tenant Isolation" 
ON guest_announcements
FOR ALL 
USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
```

## Deployment

### Vercel
1. Deploy once: `vercel --prod`
2. Add domain aliases: `vercel alias mynewhotel.hospitalityflows.com`

### Netlify
1. Deploy once
2. Add domain aliases in Netlify dashboard

### Custom Domains
1. Hotel adds CNAME: `portal.theirhotel.com → hospitalityflows.com`
2. Add SSL certificate (automatic on Vercel/Netlify)

## Cost Structure

### Single-Tenant (Old Way)
- 50 hotels × $20/month = $1,000/month
- 50 separate deployments to manage
- 50 databases to backup

### Multi-Tenant (This Way)
- 1 deployment = $20-50/month
- 1 database = $25/month
- Unlimited hotels!

## n8n Integration

### Webhook Structure
Include tenant info in all webhooks:
```json
{
  "tenant_id": "hotel-uuid",
  "tenant_subdomain": "parkhotel",
  "room": "237",
  "request": { ... }
}
```

### Routing in n8n
Use Switch node to route by tenant:
- If tenant_id = "park-hotel-uuid" → Park Hotel PMS
- If tenant_id = "ocean-view-uuid" → Ocean View PMS

## Security Considerations

1. **Data Isolation**: Always filter by tenant_id
2. **Cross-Tenant Access**: Never allow viewing other tenant's data
3. **Admin Access**: Super-admin can see all tenants
4. **API Keys**: Each tenant gets unique API keys
5. **Webhooks**: Validate tenant_id in all webhooks

## Monitoring

### Key Metrics
- Requests per tenant
- Active tenants
- Feature usage by tenant
- Error rates by tenant

### Alerts
- Tenant hitting rate limits
- Failed tenant detection
- Cross-tenant data access attempts