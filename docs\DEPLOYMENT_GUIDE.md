# Deployment Guide

## Overview

This guide provides comprehensive instructions for deploying HospitalityFlows to various platforms. The application is built with Vite and can be deployed to any static hosting service that supports Single Page Applications (SPAs).

## Table of Contents

- [Prerequisites](#prerequisites)
- [Build Configuration](#build-configuration)
- [Environment Variables](#environment-variables)
- [Platform-Specific Deployment](#platform-specific-deployment)
- [Custom Domain Setup](#custom-domain-setup)
- [Multi-Tenant Configuration](#multi-tenant-configuration)
- [SSL and Security](#ssl-and-security)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Troubleshooting](#troubleshooting)

## Prerequisites

### Required Services

1. **Supabase Account**
   - PostgreSQL database
   - Authentication service
   - Real-time subscriptions

2. **Domain Management**
   - DNS provider for subdomain configuration
   - SSL certificate management (handled by hosting platforms)

3. **Optional Services**
   - n8n instance for workflow automation
   - Analytics service (Google Analytics, Vercel Analytics)

### Local Development Setup

```bash
# Clone repository
git clone https://github.com/KrunchMuffin/hospitalityflows-demo.git
cd hospitalityflows-demo

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env

# Start development server
npm run dev
```

## Build Configuration

### Vite Configuration

The application uses Vite with the following configuration:

```typescript
// vite.config.ts
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

export default defineConfig({
  plugins: [react()],
  optimizeDeps: {
    exclude: ['lucide-react'],
  },
});
```

### Build Commands

```bash
# Development
npm run dev

# Production build
npm run build

# Preview production build locally
npm run preview

# Linting
npm run lint
```

### Build Output

- **Output Directory:** `dist/`
- **Entry Point:** `index.html`
- **Assets:** Bundled in `dist/assets/`

## Environment Variables

### Required Variables

```bash
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

### Optional Variables

```bash
# n8n Integration
VITE_N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/guest-services

# Analytics
VITE_GA_TRACKING_ID=G-XXXXXXXXXX

# Custom Configuration
VITE_APP_VERSION=1.0.0
VITE_ENVIRONMENT=production
```

### Security Notes

- Never commit `.env` files to version control
- Use platform-specific environment variable management
- Rotate keys regularly
- Use different keys for staging and production

## Platform-Specific Deployment

### Vercel (Recommended)

Vercel provides excellent support for React applications with automatic deployments.

#### Setup Steps

1. **Install Vercel CLI**
```bash
npm i -g vercel
```

2. **Deploy**
```bash
# Initial deployment
vercel

# Production deployment
vercel --prod
```

3. **Configuration**
```json
// vercel.json (optional)
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "installCommand": "npm install",
  "framework": "vite"
}
```

#### Environment Variables

Set in Vercel Dashboard:
- Project Settings → Environment Variables
- Add all required variables
- Set for Production, Preview, and Development

#### Custom Domains

```bash
# Add custom domain
vercel alias your-domain.com

# Add subdomain
vercel alias parkhotel.hospitalityflows.com
```

#### Automatic Deployments

- Connect GitHub repository
- Enable automatic deployments on push
- Configure branch-specific deployments

### Netlify

Netlify offers robust static site hosting with form handling and edge functions.

#### Setup Steps

1. **Connect Repository**
   - Go to Netlify Dashboard
   - Click "New site from Git"
   - Connect GitHub repository

2. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Node version: 18 (set in environment variables)

3. **Configuration Files**

```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
```

#### Environment Variables

Set in Netlify Dashboard:
- Site Settings → Environment Variables
- Add all required variables

#### Custom Domains

- Site Settings → Domain Management
- Add custom domain
- Configure DNS settings
- SSL certificates are automatic

### Cloudflare Pages

Cloudflare Pages provides global CDN with edge computing capabilities.

#### Setup Steps

1. **Connect Repository**
   - Go to Cloudflare Dashboard
   - Pages → Create a project
   - Connect GitHub repository

2. **Build Configuration**
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Root directory: `/` (leave empty)

3. **Configuration Files**

```
# public/_redirects
/*    /index.html   200

# public/_headers
/*
  X-Frame-Options: DENY
  X-XSS-Protection: 1; mode=block
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co wss://*.supabase.co;
```

#### Environment Variables

Set in Cloudflare Pages:
- Settings → Environment Variables
- Add production and preview variables

#### Custom Domains

- Custom domains → Add a custom domain
- Configure DNS records
- SSL is automatic

### Other Platforms

#### GitHub Pages

```yaml
# .github/workflows/deploy.yml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - run: npm install
      - run: npm run build
      - uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
```

#### AWS S3 + CloudFront

```bash
# Build and deploy to S3
npm run build
aws s3 sync dist/ s3://your-bucket-name --delete
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

## Custom Domain Setup

### DNS Configuration

#### Subdomain Setup

For tenant subdomains (e.g., `parkhotel.hospitalityflows.com`):

```
Type: CNAME
Name: parkhotel
Value: your-app-domain.com
TTL: 300
```

#### Custom Domain Setup

For hotel custom domains (e.g., `services.parkhotel.com`):

```
Type: CNAME
Name: services
Value: your-app-domain.com
TTL: 300
```

### SSL Certificates

All recommended platforms provide automatic SSL:

- **Vercel:** Automatic SSL with Let's Encrypt
- **Netlify:** Automatic SSL with Let's Encrypt
- **Cloudflare:** Universal SSL included

### Domain Verification

1. Add domain to hosting platform
2. Configure DNS records
3. Wait for SSL provisioning (usually 5-15 minutes)
4. Test HTTPS access

## Multi-Tenant Configuration

### Tenant Routing

The application automatically detects tenants based on:

1. **Subdomain:** `tenant.hospitalityflows.com`
2. **Custom Domain:** `services.hotel.com`
3. **URL Parameter:** `?tenant=demo` (development only)

### Adding New Tenants

1. **Update Configuration**
```typescript
// src/lib/tenant-config.ts
export const tenants: Record<string, Tenant> = {
  // ... existing tenants
  newhotel: {
    id: 'unique-uuid-here',
    name: 'New Hotel Name',
    subdomain: 'newhotel',
    customDomain: 'services.newhotel.com', // optional
    branding: {
      primaryColor: '#1e40af',
      portalTitle: 'Welcome to New Hotel'
    }
  }
};
```

2. **Configure DNS**
```
newhotel.hospitalityflows.com → your-app-domain.com
```

3. **Deploy Changes**
```bash
git add .
git commit -m "Add new hotel tenant"
git push origin main
```

### Database Setup

Ensure tenant data isolation:

```sql
-- Add tenant to database
INSERT INTO tenants (id, name, subdomain, settings) 
VALUES (
  'unique-uuid-here',
  'New Hotel Name',
  'newhotel',
  '{"features": {"maintenance": true, "housekeeping": true}}'
);
```

## SSL and Security

### Security Headers

Configure security headers for all platforms:

```
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
X-Content-Type-Options: nosniff
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; connect-src 'self' https://*.supabase.co;
```

### HTTPS Enforcement

All platforms automatically redirect HTTP to HTTPS.

### Environment Security

- Use environment variables for sensitive data
- Never expose API keys in client-side code
- Implement proper CORS policies
- Use Supabase RLS for data security

## Monitoring and Maintenance

### Performance Monitoring

#### Vercel Analytics

```typescript
// Add to main.tsx
import { Analytics } from '@vercel/analytics/react';

ReactDOM.render(
  <React.StrictMode>
    <App />
    <Analytics />
  </React.StrictMode>,
  document.getElementById('root')
);
```

#### Google Analytics

```typescript
// Add to index.html
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_TRACKING_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_TRACKING_ID');
</script>
```

### Error Monitoring

#### Sentry Integration

```bash
npm install @sentry/react @sentry/tracing
```

```typescript
// src/main.tsx
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
  environment: import.meta.env.MODE,
});
```

### Uptime Monitoring

Set up monitoring for:
- Application availability
- Database connectivity
- API response times
- SSL certificate expiration

### Backup Strategy

#### Database Backups

Supabase provides automated backups:
- Free tier: Point-in-time recovery for 7 days
- Pro tier: Point-in-time recovery for 30 days

#### Code Backups

- Use Git for version control
- Regular repository backups
- Tag releases for easy rollback

### Update Process

1. **Test in Development**
```bash
npm run dev
# Test all functionality
```

2. **Deploy to Staging**
```bash
vercel --target staging
# Test with production data
```

3. **Deploy to Production**
```bash
vercel --prod
# Monitor for issues
```

4. **Rollback if Needed**
```bash
vercel rollback
```

## Troubleshooting

### Common Issues

#### Build Failures

**Issue:** `Module not found` errors
```bash
# Solution: Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

**Issue:** TypeScript errors
```bash
# Solution: Check types and fix errors
npm run lint
npx tsc --noEmit
```

#### Environment Variables

**Issue:** Variables not loading
- Ensure variables start with `VITE_`
- Check platform-specific variable configuration
- Restart development server after changes

#### Routing Issues

**Issue:** 404 errors on refresh
- Ensure SPA redirects are configured
- Check `_redirects` or `vercel.json` configuration

#### SSL Issues

**Issue:** SSL certificate not provisioning
- Verify DNS configuration
- Check domain ownership
- Wait for propagation (up to 24 hours)

### Debug Tools

#### Development

```bash
# Enable debug mode
VITE_DEBUG=true npm run dev

# Check build output
npm run build
npm run preview
```

#### Production

```bash
# Check deployment logs
vercel logs
netlify logs
```

### Performance Issues

#### Bundle Size

```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist
```

#### Optimization

- Enable gzip compression
- Use CDN for static assets
- Implement code splitting
- Optimize images

### Support Resources

- [Vercel Documentation](https://vercel.com/docs)
- [Netlify Documentation](https://docs.netlify.com/)
- [Cloudflare Pages Documentation](https://developers.cloudflare.com/pages/)
- [Supabase Documentation](https://supabase.com/docs)

---

For deployment issues or questions, please check the platform-specific documentation or contact the development team.
