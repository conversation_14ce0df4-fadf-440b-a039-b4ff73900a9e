import React from 'react';
import { GuestAnnouncement } from '../../lib/supabase';

interface ActiveAnnouncementsSummaryProps {
  announcements: GuestAnnouncement[];
}

const ActiveAnnouncementsSummary: React.FC<ActiveAnnouncementsSummaryProps> = ({ announcements }) => {
  const getAnnouncementIcon = (type: string) => {
    switch (type) {
      case 'dining': return '🍽️';
      case 'event': return '🎉';
      case 'promotion': return '🎁';
      case 'alert': return '⚠️';
      default: return '📢';
    }
  };

  const isAnnouncementActive = (announcement: GuestAnnouncement) => {
    if (!announcement.is_active) return false;
    
    const now = new Date();
    const startDate = new Date(announcement.start_date);
    const endDate = announcement.end_date ? new Date(announcement.end_date) : null;
    
    return now >= startDate && (!endDate || now <= endDate);
  };

  const activeAnnouncements = announcements.filter(isAnnouncementActive);

  return (
    <div className="bg-white rounded-lg shadow-lg mb-6">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Active Announcements</h2>
      </div>
      <div className="p-6">
        <div className="space-y-2">
          {activeAnnouncements.length === 0 ? (
            <p className="text-gray-500 text-sm">No active announcements</p>
          ) : (
            activeAnnouncements.map(announcement => (
              <div key={announcement.id} className="flex items-center gap-2 text-sm">
                <span>{getAnnouncementIcon(announcement.announcement_type)}</span>
                <span className="font-medium">{announcement.headline}</span>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default ActiveAnnouncementsSummary;