import React from 'react';

export interface WorkOrder {
  id: number;
  room: string;
  service: string;
  type: string;
  description: string;
  urgency: 'Low' | 'Medium' | 'High';
  timestamp: string;
  status: 'Pending' | 'Assigned' | 'In Progress' | 'Complete';
  assignedTo?: string;
}

interface WorkOrderQueueProps {
  workOrders: WorkOrder[];
  onUpdateStatus: (id: number, status: WorkOrder['status'], assignedTo?: string) => void;
}

const WorkOrderQueue: React.FC<WorkOrderQueueProps> = ({ workOrders, onUpdateStatus }) => {
  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'High': return 'bg-red-100 text-red-800 border-red-200';
      case 'Medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'Low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Complete': return 'bg-green-100 text-green-800 border-green-200';
      case 'In Progress': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'Assigned': return 'bg-amber-100 text-amber-800 border-amber-200';
      case 'Pending': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Work Order Queue</h2>
      </div>      <div className="max-h-96 overflow-y-auto">
        {workOrders.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            No work orders at this time
          </div>
        ) : (
          workOrders.map((order) => (
            <div key={order.id} className="p-6 border-b border-gray-100 last:border-b-0">
              <div className="flex justify-between items-start mb-3">
                <div className="flex items-center gap-3">
                  <span className="font-semibold text-gray-900">Room {order.room}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(order.urgency)}`}>
                    {order.urgency}
                  </span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(order.status)}`}>
                    {order.status}
                  </span>
                </div>
                <span className="text-sm text-gray-500">
                  {new Date(order.timestamp).toLocaleTimeString()}
                </span>
              </div>
              
              <div className="mb-3">
                <p className="font-medium text-gray-800">{order.type}</p>
                <p className="text-gray-600 text-sm">{order.description}</p>
                {order.assignedTo && (
                  <p className="text-blue-600 text-sm mt-1">Assigned to: {order.assignedTo}</p>
                )}
              </div>
              
              <div className="flex gap-2">
                {order.status === 'Pending' && (
                  <button
                    onClick={() => onUpdateStatus(order.id, 'Assigned', 'John Doe')}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Assign to Me
                  </button>
                )}
                {order.status === 'Assigned' && (
                  <button
                    onClick={() => onUpdateStatus(order.id, 'In Progress')}
                    className="bg-amber-600 hover:bg-amber-700 text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Start Work
                  </button>
                )}
                {order.status === 'In Progress' && (
                  <button
                    onClick={() => onUpdateStatus(order.id, 'Complete')}
                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm transition-colors"
                  >
                    Complete
                  </button>
                )}
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

export default WorkOrderQueue;