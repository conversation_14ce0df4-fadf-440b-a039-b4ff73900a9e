import React from 'react';

const RoutingConfiguration: React.FC = () => {
  return (
    <div className="bg-white rounded-lg shadow-lg">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Call Routing</h2>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">Routing Logic</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Maintenance requests → Maintenance team</li>
              <li>• Housekeeping requests → Housekeeping supervisor</li>
              <li>• Concierge requests → Concierge desk</li>
              <li>• Emergency requests → Security first</li>
            </ul>
          </div>
          
          <div className="p-4 bg-amber-50 border border-amber-200 rounded-lg">
            <h3 className="font-medium text-amber-900 mb-2">Failover</h3>
            <p className="text-sm text-amber-800">
              If primary device is unavailable, system will automatically route to Front Desk as backup.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoutingConfiguration;