import React from 'react';
import { QRCodeSVG } from 'qrcode.react';

/**
 * <PERSON>ps interface for the RoomQRCode component
 */
interface RoomQRCodeProps {
  /** Room number to encode in the QR code URL */
  roomNumber: string;
  /** Tenant subdomain for URL generation */
  tenantSubdomain: string;
  /** QR code size in pixels (default: 128) */
  size?: number;
  /** Error correction level - higher levels allow for more damage tolerance */
  level?: 'L' | 'M' | 'Q' | 'H';
  /** Margin size around the QR code (default: 4) */
  marginSize?: number;
  /** Background color (default: white) */
  bgColor?: string;
  /** Foreground color (default: black) */
  fgColor?: string;
  /** Optional image/logo to embed in the center of the QR code */
  imageSettings?: {
    /** Image source URL */
    src: string;
    /** Image height in pixels */
    height: number;
    /** Image width in pixels */
    width: number;
    /** Whether to excavate (clear) the area behind the image */
    excavate: boolean;
    /** X position offset */
    x?: number;
    /** Y position offset */
    y?: number;
    /** Image opacity (0-1) */
    opacity?: number;
    /** CORS setting for image loading */
    crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined;
  };
}

/**
 * RoomQRCode Component
 *
 * Generates a QR code that links directly to a specific room's guest services page.
 * The QR code can be customized with different sizes, colors, and embedded logos.
 *
 * This component is used for:
 * - Printable room cards
 * - Digital displays
 * - Admin QR code generation tools
 *
 * @param props - Component props
 * @returns JSX element containing the QR code and room number label
 */
const RoomQRCode: React.FC<RoomQRCodeProps> = ({
  roomNumber,
  tenantSubdomain,
  size = 128,           // Default size for most use cases
  level = 'H',          // High error correction for printed materials
  marginSize = 4,       // Standard margin for readability
  bgColor = '#FFFFFF',  // White background
  fgColor = '#000000',  // Black foreground for maximum contrast
  imageSettings         // Optional logo embedding
}) => {
  // Construct the URL that the QR code will link to
  // This follows the pattern: https://[tenant].hospitalityflows.com/room/[roomNumber]
  const url = `https://${tenantSubdomain}.hospitalityflows.com/room/${roomNumber}`;

  return (
    <div className="flex flex-col items-center gap-2">
      {/* QR code container with white background and shadow for better visibility */}
      <div className="bg-white p-4 rounded-lg shadow-md">
        <QRCodeSVG
          value={url}                    // The URL to encode
          size={size}                    // QR code dimensions
          level={level}                  // Error correction level
          marginSize={marginSize}        // White space around QR code
          bgColor={bgColor}             // Background color
          fgColor={fgColor}             // Foreground (QR pattern) color
          imageSettings={imageSettings} // Optional embedded image/logo
          title={`QR Code for Room ${roomNumber}`} // Accessibility title
        />
      </div>

      {/* Room information and instructions */}
      <div className="text-center">
        {/* Display the room number for easy identification */}
        <p className="text-sm font-medium text-gray-700">Room {roomNumber}</p>

        {/* Instructions for guests */}
        <p className="text-xs text-gray-500 max-w-[200px] break-all">
          Scan for Guest Services
        </p>
      </div>
    </div>
  );
};

export default RoomQRCode;
