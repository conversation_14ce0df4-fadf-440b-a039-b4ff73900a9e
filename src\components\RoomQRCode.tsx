import React from 'react';
import { QRCodeSVG } from 'qrcode.react';

interface RoomQRCodeProps {
  roomNumber: string;
  tenantSubdomain: string;
  size?: number;
  level?: 'L' | 'M' | 'Q' | 'H';
  marginSize?: number;
  bgColor?: string;
  fgColor?: string;
  imageSettings?: {
    src: string;
    height: number;
    width: number;
    excavate: boolean;
    x?: number;
    y?: number;
    opacity?: number;
    crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined;
  };
}

const RoomQRCode: React.FC<RoomQRCodeProps> = ({ 
  roomNumber, 
  tenantSubdomain, 
  size = 128,
  level = 'H',
  marginSize = 4,
  bgColor = '#FFFFFF',
  fgColor = '#000000',
  imageSettings
}) => {
  const url = `https://${tenantSubdomain}.hospitalityflows.com/room/${roomNumber}`;
  
  return (
    <div className="flex flex-col items-center gap-2">
      <div className="bg-white p-4 rounded-lg shadow-md">
        <QRCodeSVG
          value={url}
          size={size}
          level={level}
          marginSize={marginSize}
          bgColor={bgColor}
          fgColor={fgColor}
          imageSettings={imageSettings}
          title={`QR Code for Room ${roomNumber}`}
        />
      </div>
      <div className="text-center">
        <p className="text-sm font-medium text-gray-700">Room {roomNumber}</p>
        <p className="text-xs text-gray-500 max-w-[200px] break-all">
          Scan for Guest Services
        </p>
      </div>
    </div>
  );
};

export default RoomQRCode;