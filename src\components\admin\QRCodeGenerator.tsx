import React, { useState } from 'react';
import { Download, Printer, Plus, X } from 'lucide-react';
import RoomQRCode from '../RoomQRCode';
import { getCurrentTenant } from '../../lib/tenant-config';

interface QRCodeGeneratorProps {
  defaultRooms?: string[];
}

const QRCodeGenerator: React.FC<QRCodeGeneratorProps> = ({ defaultRooms = [] }) => {
  const tenant = getCurrentTenant();
  const [rooms, setRooms] = useState<string[]>(defaultRooms.length > 0 ? defaultRooms : ['101']);
  const [newRoom, setNewRoom] = useState('');
  const [showNewRoomInput, setShowNewRoomInput] = useState(false);

  const addRoom = () => {
    if (newRoom && !rooms.includes(newRoom)) {
      setRooms([...rooms, newRoom]);
      setNewRoom('');
      setShowNewRoomInput(false);
    }
  };

  const removeRoom = (room: string) => {
    setRooms(rooms.filter(r => r !== room));
  };

  const handlePrint = () => {
    window.print();
  };

  const handleDownloadSingle = (roomNumber: string) => {
    // Get the SVG element
    const svg = document.querySelector(`#qr-${roomNumber} svg`) as SVGElement;
    if (svg) {
      // Convert SVG to string
      const svgData = new XMLSerializer().serializeToString(svg);
      const svgBlob = new Blob([svgData], { type: 'image/svg+xml;charset=utf-8' });
      
      // Create canvas to convert SVG to PNG
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        
        // Convert canvas to blob
        canvas.toBlob((blob) => {
          if (blob) {
            // Create download link
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `room-${roomNumber}-qr.png`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
          }
        }, 'image/png');
      };
      
      // Load SVG into image
      const svgUrl = URL.createObjectURL(svgBlob);
      img.src = svgUrl;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-bold text-gray-900">QR Code Generator</h2>
        <div className="flex gap-2">
          <button
            onClick={() => setShowNewRoomInput(true)}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add Room
          </button>
          <button
            onClick={handlePrint}
            className="flex items-center gap-2 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            <Printer className="w-4 h-4" />
            Print All
          </button>
        </div>
      </div>

      {showNewRoomInput && (
        <div className="mb-4 flex gap-2">
          <input
            type="text"
            value={newRoom}
            onChange={(e) => setNewRoom(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && addRoom()}
            placeholder="Enter room number"
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            autoFocus
          />
          <button
            onClick={addRoom}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            Add
          </button>
          <button
            onClick={() => {
              setShowNewRoomInput(false);
              setNewRoom('');
            }}
            className="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-colors"
          >
            Cancel
          </button>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {rooms.map((room) => (
          <div key={room} className="relative group">
            <div className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-300 transition-colors">
              <button
                onClick={() => removeRoom(room)}
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity bg-red-500 hover:bg-red-600 text-white p-1 rounded"
                title="Remove room"
              >
                <X className="w-4 h-4" />
              </button>
              
              <div id={`qr-${room}`}>
                <RoomQRCode
                  roomNumber={room}
                  tenantSubdomain={tenant.subdomain}
                  size={150}
                />
              </div>
              
              <button
                onClick={() => handleDownloadSingle(room)}
                className="mt-3 w-full flex items-center justify-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                <Download className="w-4 h-4" />
                Download
              </button>
            </div>
          </div>
        ))}
      </div>

      {rooms.length === 0 && (
        <p className="text-center text-gray-500 py-8">
          No rooms added. Click "Add Room" to generate QR codes.
        </p>
      )}

      {/* Print-only styles */}
      <style>{`
        @media print {
          body * {
            visibility: hidden;
          }
          .grid, .grid * {
            visibility: visible;
          }
          .grid {
            position: absolute;
            left: 0;
            top: 0;
          }
          button {
            display: none !important;
          }
        }
      `}</style>
    </div>
  );
};

export default QRCodeGenerator;