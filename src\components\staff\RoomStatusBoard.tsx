import React from 'react';

interface RoomStatusBoardProps {
  roomStatuses: Record<string, string>;
}

const RoomStatusBoard: React.FC<RoomStatusBoardProps> = ({ roomStatuses }) => {
  const getRoomStatusColor = (status: string) => {
    switch (status) {
      case 'Clean': return 'bg-green-500';
      case 'Occupied': return 'bg-blue-500';
      case 'Maintenance': return 'bg-red-500';
      case 'Housekeeping': return 'bg-yellow-500';
      default: return 'bg-gray-500';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'Clean': return 'bg-green-100 text-green-800';
      case 'Occupied': return 'bg-blue-100 text-blue-800';
      case 'Maintenance': return 'bg-red-100 text-red-800';
      case 'Housekeeping': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg mb-6">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Room Status</h2>
      </div>
      <div className="p-4">
        <div className="space-y-3">
          {Object.entries(roomStatuses).map(([room, status]) => (
            <div key={room} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-center gap-3">
                <div className={`w-4 h-4 rounded-full ${getRoomStatusColor(status)}`}></div>
                <span className="font-semibold text-gray-900">Room {room}</span>
              </div>
              <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusBadgeColor(status)}`}>
                {status}
              </span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoomStatusBoard;