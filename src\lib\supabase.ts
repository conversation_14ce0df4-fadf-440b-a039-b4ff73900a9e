import { createClient } from '@supabase/supabase-js';

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  graphql_public: {
    Tables: {
      [_ in never]: never
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      graphql: {
        Args: {
          operationName?: string
          query?: string
          variables?: Json
          extensions?: Json
        }
        Returns: Json
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
  public: {
    Tables: {
      api_credentials: {
        Row: {
          created_at: string | null
          credentials_encrypted: string
          id: string
          is_active: boolean | null
          last_verified_at: string | null
          service_name: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          credentials_encrypted: string
          id?: string
          is_active?: boolean | null
          last_verified_at?: string | null
          service_name: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          credentials_encrypted?: string
          id?: string
          is_active?: boolean | null
          last_verified_at?: string | null
          service_name?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "api_credentials_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      communication_channels: {
        Row: {
          channel_identifier: string
          channel_name: string | null
          channel_type: string
          created_at: string | null
          id: string
          is_active: boolean | null
          is_primary: boolean | null
          metadata: Json | null
          tenant_id: string
        }
        Insert: {
          channel_identifier: string
          channel_name?: string | null
          channel_type: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          is_primary?: boolean | null
          metadata?: Json | null
          tenant_id: string
        }
        Update: {
          channel_identifier?: string
          channel_name?: string | null
          channel_type?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          is_primary?: boolean | null
          metadata?: Json | null
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "communication_channels_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      departments: {
        Row: {
          code: string
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          tenant_id: string
        }
        Insert: {
          code: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          tenant_id: string
        }
        Update: {
          code?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "departments_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      device_departments: {
        Row: {
          department_id: string
          device_id: string
        }
        Insert: {
          department_id: string
          device_id: string
        }
        Update: {
          department_id?: string
          device_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "device_departments_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "device_departments_device_id_fkey"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
        ]
      }
      device_sessions: {
        Row: {
          device_id: string
          id: string
          is_active: boolean | null
          login_time: string | null
          logout_time: string | null
          user_id: string
        }
        Insert: {
          device_id: string
          id?: string
          is_active?: boolean | null
          login_time?: string | null
          logout_time?: string | null
          user_id: string
        }
        Update: {
          device_id?: string
          id?: string
          is_active?: boolean | null
          login_time?: string | null
          logout_time?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "device_sessions_device_id_fkey"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "device_sessions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      devices: {
        Row: {
          created_at: string | null
          device_identifier: string | null
          device_name: string
          device_type: string
          id: string
          is_active: boolean | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          device_identifier?: string | null
          device_name: string
          device_type: string
          id?: string
          is_active?: boolean | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          device_identifier?: string | null
          device_name?: string
          device_type?: string
          id?: string
          is_active?: boolean | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "devices_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      guest_announcements: {
        Row: {
          action_text: string | null
          action_url: string | null
          announcement_type: string | null
          background_color: string
          content: string
          created_at: string | null
          display_order: number | null
          end_date: string | null
          headline: string
          id: string
          image_url: string | null
          is_active: boolean
          metadata: Json | null
          start_date: string | null
          tenant_id: string
          text_color: string
          updated_at: string | null
        }
        Insert: {
          action_text?: string | null
          action_url?: string | null
          announcement_type?: string | null
          background_color?: string
          content?: string
          created_at?: string | null
          display_order?: number | null
          end_date?: string | null
          headline?: string
          id?: string
          image_url?: string | null
          is_active?: boolean
          metadata?: Json | null
          start_date?: string | null
          tenant_id: string
          text_color?: string
          updated_at?: string | null
        }
        Update: {
          action_text?: string | null
          action_url?: string | null
          announcement_type?: string | null
          background_color?: string
          content?: string
          created_at?: string | null
          display_order?: number | null
          end_date?: string | null
          headline?: string
          id?: string
          image_url?: string | null
          is_active?: boolean
          metadata?: Json | null
          start_date?: string | null
          tenant_id?: string
          text_color?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "header_content_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      message_logs: {
        Row: {
          channel_type: string
          created_at: string | null
          direction: string
          from_identifier: string | null
          id: string
          message_content: string | null
          metadata: Json | null
          request_id: string | null
          status: string | null
          tenant_id: string
          to_identifier: string | null
        }
        Insert: {
          channel_type: string
          created_at?: string | null
          direction: string
          from_identifier?: string | null
          id?: string
          message_content?: string | null
          metadata?: Json | null
          request_id?: string | null
          status?: string | null
          tenant_id: string
          to_identifier?: string | null
        }
        Update: {
          channel_type?: string
          created_at?: string | null
          direction?: string
          from_identifier?: string | null
          id?: string
          message_content?: string | null
          metadata?: Json | null
          request_id?: string | null
          status?: string | null
          tenant_id?: string
          to_identifier?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "message_logs_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_logs_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      message_templates: {
        Row: {
          body: string
          channel_type: string
          created_at: string | null
          id: string
          is_active: boolean | null
          subject: string | null
          template_code: string
          template_name: string
          tenant_id: string
          variables: Json | null
        }
        Insert: {
          body: string
          channel_type: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          subject?: string | null
          template_code: string
          template_name: string
          tenant_id: string
          variables?: Json | null
        }
        Update: {
          body?: string
          channel_type?: string
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          subject?: string | null
          template_code?: string
          template_name?: string
          tenant_id?: string
          variables?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "message_templates_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      request_actions: {
        Row: {
          action_data: Json | null
          action_type: string
          created_at: string | null
          device_id: string | null
          id: string
          request_id: string
          user_id: string | null
        }
        Insert: {
          action_data?: Json | null
          action_type: string
          created_at?: string | null
          device_id?: string | null
          id?: string
          request_id: string
          user_id?: string | null
        }
        Update: {
          action_data?: Json | null
          action_type?: string
          created_at?: string | null
          device_id?: string | null
          id?: string
          request_id?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "request_actions_device_id_fkey"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "request_actions_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "request_actions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      request_assignments: {
        Row: {
          assigned_at: string | null
          assigned_by_user_id: string | null
          device_id: string
          id: string
          reason: string | null
          request_id: string
          unassigned_at: string | null
        }
        Insert: {
          assigned_at?: string | null
          assigned_by_user_id?: string | null
          device_id: string
          id?: string
          reason?: string | null
          request_id: string
          unassigned_at?: string | null
        }
        Update: {
          assigned_at?: string | null
          assigned_by_user_id?: string | null
          device_id?: string
          id?: string
          reason?: string | null
          request_id?: string
          unassigned_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "request_assignments_assigned_by_user_id_fkey"
            columns: ["assigned_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "request_assignments_device_id_fkey"
            columns: ["device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "request_assignments_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      request_attachments: {
        Row: {
          created_at: string | null
          file_name: string
          file_size: number | null
          file_type: string | null
          file_url: string
          id: string
          request_id: string
          uploaded_by_user_id: string | null
        }
        Insert: {
          created_at?: string | null
          file_name: string
          file_size?: number | null
          file_type?: string | null
          file_url: string
          id?: string
          request_id: string
          uploaded_by_user_id?: string | null
        }
        Update: {
          created_at?: string | null
          file_name?: string
          file_size?: number | null
          file_type?: string | null
          file_url?: string
          id?: string
          request_id?: string
          uploaded_by_user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "request_attachments_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "request_attachments_uploaded_by_user_id_fkey"
            columns: ["uploaded_by_user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
        ]
      }
      request_types: {
        Row: {
          code: string
          department_id: string
          id: string
          is_active: boolean | null
          name: string
          priority_level: number | null
          sla_minutes: number | null
          tenant_id: string
        }
        Insert: {
          code: string
          department_id: string
          id?: string
          is_active?: boolean | null
          name: string
          priority_level?: number | null
          sla_minutes?: number | null
          tenant_id: string
        }
        Update: {
          code?: string
          department_id?: string
          id?: string
          is_active?: boolean | null
          name?: string
          priority_level?: number | null
          sla_minutes?: number | null
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "request_types_department_id_fkey"
            columns: ["department_id"]
            isOneToOne: false
            referencedRelation: "departments"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "request_types_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      requests: {
        Row: {
          assigned_at: string | null
          assigned_device_id: string | null
          completed_at: string | null
          created_at: string | null
          description: string
          guest_contact: string | null
          guest_name: string | null
          id: string
          metadata: Json | null
          priority_level: number | null
          request_number: string
          request_type_id: string
          room_number: string | null
          source: string | null
          status: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          assigned_at?: string | null
          assigned_device_id?: string | null
          completed_at?: string | null
          created_at?: string | null
          description: string
          guest_contact?: string | null
          guest_name?: string | null
          id?: string
          metadata?: Json | null
          priority_level?: number | null
          request_number: string
          request_type_id: string
          room_number?: string | null
          source?: string | null
          status?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          assigned_at?: string | null
          assigned_device_id?: string | null
          completed_at?: string | null
          created_at?: string | null
          description?: string
          guest_contact?: string | null
          guest_name?: string | null
          id?: string
          metadata?: Json | null
          priority_level?: number | null
          request_number?: string
          request_type_id?: string
          room_number?: string | null
          source?: string | null
          status?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "requests_assigned_device_id_fkey"
            columns: ["assigned_device_id"]
            isOneToOne: false
            referencedRelation: "devices"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "requests_request_type_id_fkey"
            columns: ["request_type_id"]
            isOneToOne: false
            referencedRelation: "request_types"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "requests_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      rooms: {
        Row: {
          building: string | null
          created_at: string | null
          floor: string | null
          id: string
          metadata: Json | null
          pms_room_id: string | null
          room_number: string
          room_type: string | null
          status: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          building?: string | null
          created_at?: string | null
          floor?: string | null
          id?: string
          metadata?: Json | null
          pms_room_id?: string | null
          room_number: string
          room_type?: string | null
          status?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          building?: string | null
          created_at?: string | null
          floor?: string | null
          id?: string
          metadata?: Json | null
          pms_room_id?: string | null
          room_number?: string
          room_type?: string | null
          status?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "rooms_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      tenants: {
        Row: {
          created_at: string | null
          id: string
          is_active: boolean | null
          name: string
          settings: Json | null
          subdomain: string | null
          subscription_tier: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          settings?: Json | null
          subdomain?: string | null
          subscription_tier?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          settings?: Json | null
          subdomain?: string | null
          subscription_tier?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      users: {
        Row: {
          created_at: string | null
          email: string
          id: string
          is_active: boolean | null
          name: string
          role: string | null
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          email: string
          id?: string
          is_active?: boolean | null
          name: string
          role?: string | null
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          is_active?: boolean | null
          name?: string
          role?: string | null
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "users_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      workflows: {
        Row: {
          configuration: Json
          created_at: string | null
          id: string
          is_active: boolean | null
          tenant_id: string
          trigger_type: string
          updated_at: string | null
          workflow_code: string
          workflow_name: string
        }
        Insert: {
          configuration?: Json
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          tenant_id: string
          trigger_type: string
          updated_at?: string | null
          workflow_code: string
          workflow_name: string
        }
        Update: {
          configuration?: Json
          created_at?: string | null
          id?: string
          is_active?: boolean | null
          tenant_id?: string
          trigger_type?: string
          updated_at?: string | null
          workflow_code?: string
          workflow_name?: string
        }
        Relationships: [
          {
            foreignKeyName: "workflows_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      active_guest_announcements: {
        Row: {
          action_text: string | null
          action_url: string | null
          announcement_type: string | null
          background_color: string | null
          content: string | null
          created_at: string | null
          display_order: number | null
          end_date: string | null
          headline: string | null
          id: string | null
          image_url: string | null
          is_active: boolean | null
          metadata: Json | null
          start_date: string | null
          subdomain: string | null
          tenant_id: string | null
          tenant_name: string | null
          text_color: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "header_content_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
      announcement_management: {
        Row: {
          announcement_type: string | null
          content: string | null
          created_at: string | null
          display_order: number | null
          end_date: string | null
          headline: string | null
          id: string | null
          is_active: boolean | null
          start_date: string | null
          status: string | null
          tenant_id: string | null
          tenant_name: string | null
          updated_at: string | null
        }
        Relationships: [
          {
            foreignKeyName: "header_content_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      create_announcement: {
        Args: {
          p_tenant_id: string
          p_headline: string
          p_content: string
          p_type?: string
          p_options?: Json
        }
        Returns: string
      }
      generate_request_number: {
        Args: { tenant_uuid: string }
        Returns: string
      }
      get_active_announcements: {
        Args: { p_tenant_id: string; p_announcement_type?: string }
        Returns: {
          id: string
          headline: string
          content: string
          announcement_type: string
          background_color: string
          text_color: string
          image_url: string
          action_url: string
          action_text: string
          display_order: number
          metadata: Json
        }[]
      }
      get_active_header_content: {
        Args: { p_tenant_id: string; p_page_type?: string }
        Returns: {
          id: string
          title: string
          subtitle: string
          background_color: string
          text_color: string
          logo_url: string
          hero_image_url: string
          contact_phone: string
          contact_email: string
          portal_instructions: string
          custom_css: string
          metadata: Json
        }[]
      }
      get_complete_guest_portal: {
        Args: { p_subdomain: string }
        Returns: Json
      }
      get_current_tenant_id: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_guest_portal_content: {
        Args: { p_tenant_id: string }
        Returns: Json
      }
      get_guest_portal_data: {
        Args: { p_tenant_id: string }
        Returns: Json
      }
      reorder_announcements: {
        Args: { p_tenant_id: string; p_announcement_ids: string[] }
        Returns: boolean
      }
      upsert_header_content: {
        Args: { p_tenant_id: string; p_page_type: string; p_data: Json }
        Returns: string
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  graphql_public: {
    Enums: {},
  },
  public: {
    Enums: {},
  },
} as const

// Create Supabase client
const supabaseUrl = import.meta.env.VITE_SUPABASE_URL!;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY!;

export const supabase = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Type aliases for easier usage
export type GuestAnnouncement = Database['public']['Tables']['guest_announcements']['Row'];
export type { Database };
