# HospitalityFlows Demo - Multi-Tenant Hotel Guest Services Platform

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![React](https://img.shields.io/badge/React-20232A?logo=react&logoColor=61DAFB)](https://reactjs.org/)
[![Supabase](https://img.shields.io/badge/Supabase-3ECF8E?logo=supabase&logoColor=white)](https://supabase.com/)

A modern, multi-tenant guest services platform that connects hotel systems and automates workflows. Built with React, TypeScript, Supabase, and n8n integration.

## 📋 Table of Contents

- [Overview](#overview)
- [Multi-Tenant Architecture](#-multi-tenant-architecture)
- [Key Features](#-key-features)
- [Technology Stack](#️-technology-stack)
- [Project Structure](#-project-structure)
- [Installation](#-installation)
- [Configuration](#-configuration)
- [Usage](#-usage)
- [API Documentation](#-api-documentation)
- [Deployment](#-deployment)
- [Contributing](#-contributing)
- [License](#-license)

## Overview

HospitalityFlows is a comprehensive guest services platform designed for the modern hospitality industry. It enables hotels to provide seamless digital experiences to their guests while maintaining operational efficiency through automation and real-time communication.

## 🏨 Multi-Tenant Architecture

**One platform, unlimited hotels!** Each hotel gets:
- Custom subdomain (e.g., `parkhotel.hospitalityflows.com`)
- Optional custom domain (e.g., `services.parkhotel.com`)
- Isolated data and settings
- Custom branding and features
- Individual webhook endpoints

### Quick Start - Testing Different Hotels

In development, test different hotels using URL parameters:
```
http://localhost:3000?tenant=demo      # Grand Plaza Hotel
http://localhost:3000?tenant=parkhotel  # Park Hotel & Spa
http://localhost:3000?tenant=oceanview  # Ocean View Resort
```

## 🚀 Key Features

### For Guests
- **Instant Service Requests** - Report issues, request housekeeping, or contact concierge
- **Smart Forms** - Pre-populated with guest information from PMS
- **Real-time Updates** - Instant staff notifications via n8n webhooks
- **Mobile Responsive** - Works on any device

### For Hotels
- **Dynamic Announcements** - Manage multiple announcements with scheduling
- **Service Toggle** - Enable/disable features per hotel
- **Custom Branding** - Colors, logos, and messaging
- **Analytics Ready** - Track service usage and response times
- **QR Code Generation** - Create QR codes for all rooms with branding
- **Printable Room Cards** - Professional cards with QR codes and hotel info

### For Administrators
- **Single Dashboard** - Manage all hotels from one place
- **Tenant Isolation** - Secure data separation
- **Easy Onboarding** - Add new hotels in minutes
- **Cost Efficient** - One infrastructure for all clients
- **Bulk QR Generator** - Generate and download QR codes for all rooms

## 🛠️ Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Realtime)
- **Integration**: n8n workflows
- **Icons**: Lucide React
- **Routing**: React Router v7
- **State**: React Hooks
- **Build Tool**: Vite
- **Linting**: ESLint with TypeScript support
- **QR Codes**: qrcode.react
- **Deployment**: Vercel, Netlify, Cloudflare Pages

## 📁 Project Structure

```
hospitalityflows-demo/
├── public/                     # Static assets
│   ├── _headers               # Security headers for deployment
│   └── _redirects             # SPA routing redirects
├── src/
│   ├── components/            # Reusable React components
│   │   ├── admin/            # Admin panel components
│   │   ├── guest/            # Guest-facing components
│   │   ├── staff/            # Staff dashboard components
│   │   ├── AdvancedRoomQRCode.tsx
│   │   ├── Navigation.tsx
│   │   ├── PrintableRoomCard.tsx
│   │   ├── RoomQRCode.tsx
│   │   └── TenantDebugger.tsx
│   ├── contexts/             # React contexts
│   │   └── ToastContext.tsx
│   ├── lib/                  # Utility libraries
│   │   ├── supabase.ts      # Supabase client and types
│   │   └── tenant-config.ts  # Multi-tenant configuration
│   ├── pages/               # Main application pages
│   │   ├── AdminPanel.tsx
│   │   ├── DemoShowcase.tsx
│   │   ├── GuestServicesPage.tsx
│   │   ├── HomePage.tsx
│   │   └── StaffDashboard.tsx
│   ├── App.tsx              # Main application component
│   ├── main.tsx             # Application entry point
│   └── index.css            # Global styles
├── supabase/
│   └── migrations/          # Database migrations
├── .docs/                   # Additional documentation
│   ├── MULTI_TENANT_GUIDE.md
│   ├── CLOUDFLARE_DEPLOYMENT.md
│   ├── QR_CODE_IMPLEMENTATION.md
│   └── PRODUCTION_DEPLOYMENT_CHECKLIST.md
├── package.json
├── vite.config.ts
├── tailwind.config.js
├── tsconfig.json
└── README.md
```

## 📦 Installation

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account
- Git

### Step-by-Step Setup

1. **Clone the repository:**
```bash
git clone https://github.com/KrunchMuffin/hospitalityflows-demo.git
cd hospitalityflows-demo
```

2. **Install dependencies:**
```bash
npm install
```

3. **Set up environment variables:**
Create a `.env` file in the root directory:
```bash
# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: n8n Webhook URL
VITE_N8N_WEBHOOK_URL=your_n8n_webhook_url
```

4. **Set up Supabase database:**
   - Create a new Supabase project
   - Run the migration files in `supabase/migrations/`
   - Enable Row Level Security (RLS) on all tables
   - Set up the required database schema (see [Database Schema](#database-schema))

5. **Start the development server:**
```bash
npm run dev
```

6. **Access the application:**
   - Main app: `http://localhost:5173`
   - Demo tenant: `http://localhost:5173?tenant=demo`
   - Guest services: `http://localhost:5173/room/237`

### Verification

After setup, verify the installation by:
- [ ] Accessing the homepage
- [ ] Testing different tenants with URL parameters
- [ ] Checking the tenant debugger in the bottom-right corner
- [ ] Accessing a guest room page (e.g., `/room/237`)
- [ ] Viewing the staff dashboard at `/staff`

## ⚙️ Configuration

### Environment Variables

| Variable | Description | Required | Example |
|----------|-------------|----------|---------|
| `VITE_SUPABASE_URL` | Supabase project URL | Yes | `https://xyz.supabase.co` |
| `VITE_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| `VITE_N8N_WEBHOOK_URL` | n8n webhook endpoint | No | `https://n8n.example.com/webhook/...` |

### Tenant Configuration

Each hotel tenant is configured in `src/lib/tenant-config.ts`. Here's the structure:

```typescript
interface Tenant {
  id: string;                    // Unique UUID for the tenant
  name: string;                  // Display name
  subdomain: string;             // Subdomain identifier
  customDomain?: string;         // Optional custom domain
  branding?: {
    primaryColor?: string;       // Main brand color
    secondaryColor?: string;     // Secondary brand color
    logo?: string;              // Logo URL
    portalTitle?: string;       // Portal headline
    portalSubtitle?: string;    // Portal subtitle
  };
  settings?: {
    timezone?: string;          // Hotel timezone
    currency?: string;          // Currency code
    language?: string;          // Language code
    features?: {               // Feature toggles
      maintenance?: boolean;
      housekeeping?: boolean;
      concierge?: boolean;
      announcements?: boolean;
    };
  };
  contact?: {
    phone?: string;            // Contact phone
    email?: string;            // Contact email
    address?: string;          // Hotel address
  };
}
```

### 🏗️ Adding a New Hotel

1. **Edit `src/lib/tenant-config.ts`:**
```typescript
newhotel: {
  id: 'unique-uuid-here',
  name: 'New Hotel Name',
  subdomain: 'newhotel',
  branding: {
    primaryColor: '#1e40af',
    portalTitle: 'Welcome to New Hotel'
  },
  settings: {
    features: {
      maintenance: true,
      housekeeping: true,
      concierge: true,
      announcements: true
    }
  }
}
```

2. **Configure DNS:**
```
newhotel.hospitalityflows.com → your-app-domain.com
```

3. **That's it!** The hotel is live and accessible.

## 🚀 Usage

### For Guests

1. **Access the guest portal:**
   - Scan QR code in hotel room
   - Or visit: `https://[hotel].hospitalityflows.com/room/[room-number]`

2. **Submit service requests:**
   - Choose service type (maintenance, housekeeping, concierge)
   - Fill out the request form
   - Receive confirmation and updates

### For Hotel Staff

1. **Monitor requests:**
   - Visit: `https://[hotel].hospitalityflows.com/staff`
   - View real-time activity feed
   - Track request statuses

2. **Manage announcements:**
   - Visit: `https://[hotel].hospitalityflows.com/admin`
   - Create/edit guest announcements
   - Schedule announcements

### For Administrators

1. **Generate QR codes:**
   - Use the admin panel QR code generator
   - Download individual or bulk QR codes
   - Print room cards with QR codes

2. **Manage tenants:**
   - Edit tenant configurations
   - Enable/disable features
   - Customize branding

## 📡 API Documentation

### Database Schema

The application uses Supabase with the following main tables:

#### `tenants`
```sql
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  subdomain TEXT UNIQUE NOT NULL,
  custom_domain TEXT,
  settings JSONB,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

#### `guest_announcements`
```sql
CREATE TABLE guest_announcements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  headline TEXT NOT NULL,
  content TEXT NOT NULL,
  background_color TEXT DEFAULT '#1e40af',
  text_color TEXT DEFAULT '#ffffff',
  is_active BOOLEAN DEFAULT true,
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

#### `requests`
```sql
CREATE TABLE requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  request_number TEXT NOT NULL,
  request_type_id UUID REFERENCES request_types(id),
  room_number TEXT,
  description TEXT NOT NULL,
  status TEXT DEFAULT 'pending',
  priority_level INTEGER DEFAULT 1,
  guest_name TEXT,
  guest_contact TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);
```

### 🔄 n8n Integration

The platform sends webhooks to n8n with tenant information:

```json
{
  "tenant_id": "hotel-uuid",
  "tenant_subdomain": "parkhotel",
  "hotel_name": "Park Hotel & Spa",
  "room": "237",
  "service": "maintenance",
  "type": "Air Conditioning",
  "description": "AC not cooling",
  "urgency": "High",
  "guest_name": "John Doe",
  "guest_contact": "<EMAIL>",
  "timestamp": "2025-01-01T12:00:00Z"
}
```

Use this in n8n to route requests to the appropriate hotel's systems.

## 📱 QR Code Features

### Dynamic QR Code Generation
- Generate QR codes for any room number
- Customize with hotel branding and colors
- Embed hotel logos in QR codes
- Download as PNG for printing

### QR Code Components
- **Basic QR Code** - Simple room QR with label
- **Advanced QR Code** - Styled with branding options
- **Printable Room Cards** - Complete cards with WiFi info
- **Bulk Generator** - Create QR codes for all rooms at once

### Usage
```tsx
// Basic QR Code
<RoomQRCode roomNumber="237" tenantSubdomain="demo" />

// With Hotel Logo
<AdvancedRoomQRCode
  roomNumber="237"
  hotelName="Grand Plaza Hotel"
  logoUrl="/logo.png"
  style="modern"
/>
```

## 📱 Application Routes

| Route | Description | Access Level | Features |
|-------|-------------|--------------|----------|
| `/` | Hotel homepage | Public | Tenant branding, featured room link, announcements |
| `/room/:roomNumber` | Guest services portal | Public | Service requests, room-specific content |
| `/staff` | Staff dashboard | Internal | Live activity feed, request monitoring |
| `/admin` | Admin panel | Internal | Announcement management, QR code generation |
| `/demo` | Interactive demo | Public | Feature showcase, demo scenarios |

### Page Details

#### Homepage (`/`)
- Displays tenant-specific branding and welcome message
- Shows active announcements
- Provides quick access to featured room
- Responsive design for all devices

#### Guest Services (`/room/:roomNumber`)
- Dynamic room number from URL parameter
- Service request forms (maintenance, housekeeping, concierge)
- Real-time status updates
- Mobile-optimized interface

#### Staff Dashboard (`/staff`)
- Live activity feed with real-time updates
- Request status monitoring
- Room status overview
- Tenant-specific data filtering

#### Admin Panel (`/admin`)
- Announcement creation and management
- QR code generator for rooms
- Bulk operations for multiple rooms
- Tenant configuration tools

## 🔐 Security

- Row Level Security (RLS) on all tables
- Tenant isolation at database level
- Secure webhook endpoints
- Input validation and sanitization

## 🚀 Deployment

### Build Commands

```bash
# Development
npm run dev

# Production build
npm run build

# Preview production build
npm run preview

# Linting
npm run lint
```

### Platform-Specific Deployment

#### Vercel (Recommended)
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Add custom domains
vercel alias parkhotel.hospitalityflows.com
vercel alias services.parkhotel.com
```

**Vercel Configuration:**
- Build Command: `npm run build`
- Output Directory: `dist`
- Install Command: `npm install`

#### Netlify
1. Connect GitHub repository
2. Set build settings:
   - Build command: `npm run build`
   - Publish directory: `dist`
3. Add domain aliases in Netlify dashboard
4. Configure environment variables

#### Cloudflare Pages
1. Connect GitHub repository
2. Set build configuration:
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Node version: 18+
3. Add environment variables
4. Configure custom domains

### Environment Variables for Production

Set these in your deployment platform:

```bash
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key
VITE_N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/guest-services
```

### Custom Domains Setup

1. **Hotel adds CNAME record:**
```
portal.theirhotel.com → hospitalityflows.com
```

2. **Platform handles SSL automatically** (Vercel/Netlify/Cloudflare)

3. **Update tenant configuration** to include custom domain

## 📊 Cost Benefits

**Traditional Approach:**
- 50 hotels × $20/month = $1,000/month
- 50 separate codebases to maintain

**Multi-Tenant Approach:**
- 1 platform = ~$50/month total
- Unlimited hotels
- Single codebase

## 🧪 Development Workflow

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Configured with React and TypeScript rules
- **Prettier**: Code formatting (if configured)
- **Naming**: Use descriptive names for components and functions

### Testing

```bash
# Run linting
npm run lint

# Fix linting issues
npm run lint -- --fix

# Type checking
npx tsc --noEmit
```

### Component Development

1. **Create components in appropriate directories:**
   - `src/components/guest/` - Guest-facing components
   - `src/components/staff/` - Staff dashboard components
   - `src/components/admin/` - Admin panel components

2. **Follow naming conventions:**
   - PascalCase for component files
   - camelCase for utility functions
   - kebab-case for CSS classes

3. **Add TypeScript interfaces for props**

### Database Changes

1. Create migration files in `supabase/migrations/`
2. Test migrations locally
3. Update TypeScript types in `src/lib/supabase.ts`

## 🤝 Contributing

We welcome contributions! Please follow these steps:

### Getting Started

1. **Fork the repository**
2. **Clone your fork:**
```bash
git clone https://github.com/your-username/hospitalityflows-demo.git
cd hospitalityflows-demo
```

3. **Create a feature branch:**
```bash
git checkout -b feature/amazing-feature
```

4. **Install dependencies:**
```bash
npm install
```

### Making Changes

1. **Follow the code standards** outlined above
2. **Test your changes** thoroughly
3. **Update documentation** if needed
4. **Add comments** for complex logic

### Submitting Changes

1. **Commit your changes:**
```bash
git commit -m 'feat: add amazing feature'
```

2. **Push to your branch:**
```bash
git push origin feature/amazing-feature
```

3. **Open a Pull Request** with:
   - Clear description of changes
   - Screenshots for UI changes
   - Testing instructions

### Commit Message Format

Use conventional commits:
- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes
- `refactor:` - Code refactoring
- `test:` - Adding tests
- `chore:` - Maintenance tasks

## 🔧 Troubleshooting

### Common Issues

#### Environment Variables Not Loading
```bash
# Ensure .env file is in root directory
# Restart development server after changes
npm run dev
```

#### Supabase Connection Issues
- Verify `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY`
- Check Supabase project status
- Ensure RLS policies are configured correctly

#### Tenant Not Found
- Check tenant configuration in `src/lib/tenant-config.ts`
- Verify subdomain/domain mapping
- Clear tenant cache in development

#### QR Code Generation Issues
- Ensure `qrcode.react` is installed
- Check room number format
- Verify tenant subdomain is correct

### Getting Help

- Check existing [Issues](https://github.com/KrunchMuffin/hospitalityflows-demo/issues)
- Review [Documentation](.docs/)
- Contact the development team

## 📚 Additional Documentation

- [Multi-Tenant Guide](.docs/MULTI_TENANT_GUIDE.md) - Detailed multi-tenancy documentation
- [QR Code Implementation](.docs/QR_CODE_IMPLEMENTATION.md) - QR code features and usage
- [Deployment Guide](.docs/CLOUDFLARE_DEPLOYMENT.md) - Platform-specific deployment instructions
- [Production Checklist](.docs/PRODUCTION_DEPLOYMENT_CHECKLIST.md) - Pre-production requirements

## 📊 Performance & Monitoring

### Key Metrics
- Page load times
- API response times
- Database query performance
- User engagement per tenant

### Monitoring Tools
- Supabase Dashboard for database metrics
- Vercel Analytics for web performance
- Custom logging for tenant-specific metrics

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Built for the hospitality industry's digital transformation
- Inspired by the need for better hotel-guest communication
- Powered by modern web technologies
- Special thanks to the open-source community

## 🚀 Quick Start Demo

**Demo Credentials:**
- Guest Portal: Visit `/room/237` (or any room number)
- Staff Dashboard: Visit `/staff`
- Admin Panel: Visit `/admin`

**Test Different Hotels:**
- Demo Hotel: `?tenant=demo`
- Park Hotel: `?tenant=parkhotel`
- Ocean View: `?tenant=oceanview`

---

**Built with ❤️ for the hospitality industry**

For questions or support, please [open an issue](https://github.com/KrunchMuffin/hospitalityflows-demo/issues) or contact the development team.
