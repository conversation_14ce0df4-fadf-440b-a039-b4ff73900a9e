# HospitalityFlows Demo - Multi-Tenant Hotel Guest Services Platform

A modern, multi-tenant guest services platform that connects hotel systems and automates workflows. Built with React, TypeScript, Supabase, and n8n integration.

## 🏨 Multi-Tenant Architecture

**One platform, unlimited hotels!** Each hotel gets:
- Custom subdomain (e.g., `parkhotel.hospitalityflows.com`)
- Optional custom domain (e.g., `services.parkhotel.com`)
- Isolated data and settings
- Custom branding and features
- Individual webhook endpoints

### Quick Start - Testing Different Hotels

In development, test different hotels using URL parameters:
```
http://localhost:3000?tenant=demo      # Grand Plaza Hotel
http://localhost:3000?tenant=parkhotel  # Park Hotel & Spa
http://localhost:3000?tenant=oceanview  # Ocean View Resort
```

## 🚀 Key Features

### For Guests
- **Instant Service Requests** - Report issues, request housekeeping, or contact concierge
- **Smart Forms** - Pre-populated with guest information from PMS
- **Real-time Updates** - Instant staff notifications via n8n webhooks
- **Mobile Responsive** - Works on any device

### For Hotels
- **Dynamic Announcements** - Manage multiple announcements with scheduling
- **Service Toggle** - Enable/disable features per hotel
- **Custom Branding** - Colors, logos, and messaging
- **Analytics Ready** - Track service usage and response times
- **QR Code Generation** - Create QR codes for all rooms with branding
- **Printable Room Cards** - Professional cards with QR codes and hotel info

### For Administrators
- **Single Dashboard** - Manage all hotels from one place
- **Tenant Isolation** - Secure data separation
- **Easy Onboarding** - Add new hotels in minutes
- **Cost Efficient** - One infrastructure for all clients
- **Bulk QR Generator** - Generate and download QR codes for all rooms

## 🛠️ Technology Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth + Realtime)
- **Integration**: n8n workflows
- **Icons**: Lucide React
- **Routing**: React Router v6
- **State**: React Hooks

## 📦 Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/hospitalityflows-demo.git
cd hospitalityflows-demo
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env
```

Add your Supabase credentials:
```
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

4. Set up the database:
```sql
-- Run the SQL from schema.sql to create tables
-- Includes guest_announcements table with tenant support
```

5. Start the development server:
```bash
npm run dev
```

## 🏗️ Adding a New Hotel

1. Edit `src/lib/tenant-config.ts`:
```typescript
newhotel: {
  id: 'unique-uuid',
  name: 'New Hotel Name',
  subdomain: 'newhotel',
  branding: {
    primaryColor: '#yourcolor',
    portalTitle: 'Welcome to New Hotel'
  }
}
```

2. Configure DNS:
```
newhotel.hospitalityflows.com → your-app-domain.com
```

3. That's it! The hotel is live.

## 🔄 n8n Integration

The platform sends webhooks to n8n with tenant information:

```json
{
  "tenant_id": "hotel-uuid",
  "tenant_subdomain": "parkhotel",
  "hotel_name": "Park Hotel & Spa",
  "room": "237",
  "service": "maintenance",
  "type": "Air Conditioning",
  "description": "AC not cooling",
  "urgency": "High"
}
```

Use this in n8n to route requests to the appropriate hotel's systems.

## 📱 QR Code Features

### Dynamic QR Code Generation
- Generate QR codes for any room number
- Customize with hotel branding and colors
- Embed hotel logos in QR codes
- Download as PNG for printing

### QR Code Components
- **Basic QR Code** - Simple room QR with label
- **Advanced QR Code** - Styled with branding options
- **Printable Room Cards** - Complete cards with WiFi info
- **Bulk Generator** - Create QR codes for all rooms at once

### Usage
```tsx
// Basic QR Code
<RoomQRCode roomNumber="237" tenantSubdomain="demo" />

// With Hotel Logo
<AdvancedRoomQRCode 
  roomNumber="237"
  hotelName="Grand Plaza Hotel"
  logoUrl="/logo.png"
  style="modern"
/>
```

## 📱 Pages

- `/` - Hotel homepage (customizable per tenant)
- `/room/:roomNumber` - Guest services portal (e.g., `/room/237`)
- `/staff` - Staff dashboard for monitoring requests
- `/admin` - Admin panel for managing announcements
- `/demo` - Interactive demo showcase

## 🔐 Security

- Row Level Security (RLS) on all tables
- Tenant isolation at database level
- Secure webhook endpoints
- Input validation and sanitization

## 🚀 Deployment

### Vercel
```bash
vercel --prod
vercel alias parkhotel.hospitalityflows.com
```

### Netlify
1. Connect GitHub repo
2. Add domain aliases in settings

### Custom Domains
1. Hotel adds CNAME record
2. Platform handles SSL automatically

## 📊 Cost Benefits

**Traditional Approach:**
- 50 hotels × $20/month = $1,000/month
- 50 separate codebases to maintain

**Multi-Tenant Approach:**
- 1 platform = ~$50/month total
- Unlimited hotels
- Single codebase

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

- Built for the hospitality industry's digital transformation
- Inspired by the need for better hotel-guest communication
- Powered by modern web technologies

---

**Demo Credentials:**
- Guest Portal: Visit `/room/237` (or any room number)
- Staff Dashboard: Visit `/staff`
- Admin Panel: Visit `/admin`

For more detailed documentation, see [MULTI_TENANT_GUIDE.md](./MULTI_TENANT_GUIDE.md)