import React from 'react';
import { QRCodeSVG } from 'qrcode.react';
import { Wifi, Phone, Clock, Smartphone } from 'lucide-react';

interface PrintableRoomCardProps {
  roomNumber: string;
  tenantSubdomain: string;
  hotelName: string;
  hotelLogo?: string;
  wifiName?: string;
  wifiPassword?: string;
  frontDeskPhone?: string;
  primaryColor?: string;
}

const PrintableRoomCard: React.FC<PrintableRoomCardProps> = ({
  roomNumber,
  tenantSubdomain,
  hotelName,
  hotelLogo,
  wifiName = 'Hotel_WiFi',
  wifiPassword = 'password123',
  frontDeskPhone = 'Dial 0',
  primaryColor = '#1e40af'
}) => {
  const url = `https://${tenantSubdomain}.hospitalityflows.com/room/${roomNumber}`;

  return (
    <div className="print-card bg-white p-8 max-w-sm mx-auto" style={{ pageBreakInside: 'avoid' }}>
      {/* Header */}
      <div className="text-center mb-6">
        {hotelLogo && (
          <img src={hotelLogo} alt={hotelName} className="h-12 mx-auto mb-3" />
        )}
        <h2 className="text-2xl font-bold" style={{ color: primaryColor }}>
          {hotelName}
        </h2>
        <p className="text-gray-600 mt-1">Room {roomNumber}</p>
      </div>

      {/* QR Code Section */}
      <div className="bg-gray-50 rounded-lg p-6 mb-6">
        <h3 className="text-center font-semibold mb-4" style={{ color: primaryColor }}>
          Instant Guest Services
        </h3>
        
        <div className="flex justify-center mb-4">
          <QRCodeSVG
            value={url}
            size={180}
            level="H"
            marginSize={4}
            fgColor={primaryColor}
            imageSettings={hotelLogo ? {
              src: hotelLogo,
              height: 36,
              width: 36,
              excavate: true,
            } : undefined}
          />
        </div>

        <div className="text-center">
          <p className="font-medium mb-2">Scan for:</p>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• Maintenance Requests</li>
            <li>• Housekeeping Service</li>
            <li>• Concierge Assistance</li>
            <li>• Hotel Information</li>
          </ul>
        </div>
      </div>

      {/* Quick Info */}
      <div className="border-t pt-4 space-y-3">
        <div className="flex items-center gap-3">
          <Wifi className="w-5 h-5 text-gray-400" />
          <div className="text-sm">
            <p className="font-medium">WiFi Network: {wifiName}</p>
            <p className="text-gray-600">Password: {wifiPassword}</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Phone className="w-5 h-5 text-gray-400" />
          <div className="text-sm">
            <p className="font-medium">Front Desk: {frontDeskPhone}</p>
          </div>
        </div>

        <div className="flex items-center gap-3">
          <Clock className="w-5 h-5 text-gray-400" />
          <div className="text-sm">
            <p className="font-medium">Check-out: 11:00 AM</p>
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div className="mt-6 p-4 bg-blue-50 rounded-lg" style={{ backgroundColor: `${primaryColor}10` }}>
        <div className="flex items-start gap-2">
          <Smartphone className="w-4 h-4 mt-0.5" style={{ color: primaryColor }} />
          <div className="text-xs">
            <p className="font-medium mb-1" style={{ color: primaryColor }}>
              How to use:
            </p>
            <ol className="text-gray-600 space-y-0.5">
              <li>1. Open your phone's camera</li>
              <li>2. Point at the QR code above</li>
              <li>3. Tap the notification that appears</li>
              <li>4. Select the service you need</li>
            </ol>
          </div>
        </div>
      </div>

      {/* Print-only styles */}
      <style>{`
        @media print {
          .print-card {
            width: 4in;
            height: 6in;
            margin: 0;
            padding: 0.5in;
            page-break-after: always;
          }
          
          body {
            margin: 0;
          }
        }
      `}</style>
    </div>
  );
};

export default PrintableRoomCard;