// Define feature keys as a separate type
export type FeatureKey = 'maintenance' | 'housekeeping' | 'concierge' | 'announcements';

// Tenant configuration for multi-tenant support
export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  customDomain?: string;
  branding?: {
    primaryColor?: string;
    secondaryColor?: string;
    logo?: string;
    portalTitle?: string;
    portalSubtitle?: string;
  };
  settings?: {
    timezone?: string;
    currency?: string;
    language?: string;
    features?: {
      maintenance?: boolean;
      housekeeping?: boolean;
      concierge?: boolean;
      announcements?: boolean;
    };
    demoConfig?: {
      featuredRoom?: string;
      demoRooms?: Record<string, string>;
    };
  };
  contact?: {
    phone?: string;
    email?: string;
    address?: string;
  };
}

// Tenant database - in production this would come from your database
export const tenants: Record<string, Tenant> = {
  demo: {
    id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
    name: 'Grand Plaza Hotel',
    subdomain: 'demo',
    branding: {
      primaryColor: '#1e40af',
      secondaryColor: '#f59e0b',
      portalTitle: 'Welcome to Grand Plaza Hotel',
      portalSubtitle: 'Luxury service at your fingertips'
    },
    settings: {
      timezone: 'America/New_York',
      currency: 'USD',
      language: 'en',
      features: {
        maintenance: true,
        housekeeping: true,
        concierge: true,
        announcements: true
      },
      demoConfig: {
        featuredRoom: '237',
        demoRooms: {
          '235': 'Clean',
          '236': 'Occupied',
          '237': 'Maintenance',
          '238': 'Clean',
          '239': 'Occupied',
          '240': 'Housekeeping'
        }
      }
    },
    contact: {
      phone: '+****************',
      email: '<EMAIL>',
      address: '123 Plaza Street, New York, NY 10001'
    }
  },
  parkhotel: {
    id: 'park-hotel-uuid',
    name: 'Park Hotel & Spa',
    subdomain: 'parkhotel',
    customDomain: 'services.parkhotel.com',
    branding: {
      primaryColor: '#059669',
      secondaryColor: '#d97706',
      portalTitle: 'Park Hotel Guest Services',
      portalSubtitle: 'Your comfort is our priority'
    },
    settings: {
      timezone: 'America/Chicago',
      currency: 'USD',
      language: 'en',
      features: {
        maintenance: true,
        housekeeping: true,
        concierge: true,
        announcements: true
      }
    },
    contact: {
      phone: '+****************',
      email: '<EMAIL>',
      address: '456 Park Avenue, Chicago, IL 60601'
    }
  },
  oceanview: {
    id: 'ocean-view-uuid',
    name: 'Ocean View Resort',
    subdomain: 'oceanview',
    branding: {
      primaryColor: '#0891b2',
      secondaryColor: '#f59e0b',
      portalTitle: 'Ocean View Resort Portal',
      portalSubtitle: 'Paradise awaits'
    },
    settings: {
      timezone: 'America/Los_Angeles',
      currency: 'USD',
      language: 'en',
      features: {
        maintenance: true,
        housekeeping: true,
        concierge: true,
        announcements: true
      }
    },
    contact: {
      phone: '+****************',
      email: '<EMAIL>',
      address: '789 Beach Boulevard, San Diego, CA 92101'
    }
  }
};

// Cache the current tenant to avoid repeated lookups
let cachedTenant: Tenant | null = null;

// Get current tenant based on subdomain or custom domain
export function getCurrentTenant(): Tenant {
  // Return cached tenant if available
  if (cachedTenant) return cachedTenant;

  const hostname = window.location.hostname;
  
  // Development environment
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // Check URL params for tenant override in development
    const urlParams = new URLSearchParams(window.location.search);
    const tenantParam = urlParams.get('tenant');
    if (tenantParam && tenants[tenantParam]) {
      cachedTenant = tenants[tenantParam];
      return cachedTenant;
    }
    // Default to demo in development
    cachedTenant = tenants.demo;
    return cachedTenant;
  }
  
  // Check if it's a custom domain
  const customTenant = Object.values(tenants).find(
    tenant => tenant.customDomain === hostname
  );
  if (customTenant) {
    cachedTenant = customTenant;
    return cachedTenant;
  }
  
  // Check subdomain (e.g., parkhotel.hospitalityflows.com)
  const parts = hostname.split('.');
  if (parts.length >= 3) { // Has subdomain
    const subdomain = parts[0];
    const tenant = Object.values(tenants).find(
      t => t.subdomain === subdomain
    );
    if (tenant) {
      cachedTenant = tenant;
      return cachedTenant;
    }
  }
  
  // Fallback to demo tenant
  console.warn(`No tenant found for hostname: ${hostname}, using demo tenant`);
  cachedTenant = tenants.demo;
  return cachedTenant;
}

// Clear tenant cache (useful when switching tenants in development)
export function clearTenantCache() {
  cachedTenant = null;
}

// Get tenant by ID
export function getTenantById(id: string): Tenant | undefined {
  return Object.values(tenants).find(tenant => tenant.id === id);
}

// Get all tenants (useful for admin interfaces)
export function getAllTenants(): Tenant[] {
  return Object.values(tenants);
}

// Check if a feature is enabled for the current tenant
export function isFeatureEnabled(feature: FeatureKey): boolean {
  const tenant = getCurrentTenant();
  return tenant.settings?.features?.[feature] ?? true; // Default to true if not specified
}

// Legacy exports for backward compatibility
export const DEMO_TENANT_ID = tenants.demo.id;
export const DEMO_SUBDOMAIN = tenants.demo.subdomain;
export const DEFAULT_PORTAL_BRANDING = {
  logo_url: tenants.demo.branding?.logo || null,
  primary_color: tenants.demo.branding?.primaryColor || '#1e40af',
  secondary_color: tenants.demo.branding?.secondaryColor || '#3b82f6',
  portal_title: tenants.demo.branding?.portalTitle || 'Guest Services Portal',
  welcome_message: 'How can we help you today?',
  contact_phone: tenants.demo.contact?.phone || '+****************',
  contact_email: tenants.demo.contact?.email || '<EMAIL>'
};