/**
 * Feature keys that can be enabled/disabled per tenant
 * Used for feature toggles throughout the application
 */
export type FeatureKey = 'maintenance' | 'housekeeping' | 'concierge' | 'announcements';

/**
 * Tenant configuration interface for multi-tenant support
 *
 * Each hotel/tenant has their own configuration that controls:
 * - Branding and visual appearance
 * - Available features and services
 * - Contact information and settings
 * - Demo-specific configurations
 */
export interface Tenant {
  /** Unique identifier for the tenant (UUID) */
  id: string;

  /** Display name of the hotel/tenant */
  name: string;

  /** Subdomain identifier (e.g., 'demo' for demo.hospitalityflows.com) */
  subdomain: string;

  /** Optional custom domain (e.g., 'services.parkhotel.com') */
  customDomain?: string;

  /** Visual branding configuration */
  branding?: {
    /** Primary brand color (hex code) */
    primaryColor?: string;
    /** Secondary brand color (hex code) */
    secondaryColor?: string;
    /** URL to hotel logo image */
    logo?: string;
    /** Main headline for guest portal */
    portalTitle?: string;
    /** Subtitle/tagline for guest portal */
    portalSubtitle?: string;
  };

  /** Operational settings and feature toggles */
  settings?: {
    /** Hotel timezone (e.g., 'America/New_York') */
    timezone?: string;
    /** Currency code (e.g., 'USD') */
    currency?: string;
    /** Language code (e.g., 'en') */
    language?: string;

    /** Feature availability toggles */
    features?: {
      /** Enable maintenance request service */
      maintenance?: boolean;
      /** Enable housekeeping request service */
      housekeeping?: boolean;
      /** Enable concierge service */
      concierge?: boolean;
      /** Enable guest announcements */
      announcements?: boolean;
    };

    /** Demo-specific configuration (used for demo tenant) */
    demoConfig?: {
      /** Featured room number for homepage */
      featuredRoom?: string;
      /** Demo room numbers with their statuses */
      demoRooms?: Record<string, string>;
    };
  };

  /** Hotel contact information */
  contact?: {
    /** Contact phone number */
    phone?: string;
    /** Contact email address */
    email?: string;
    /** Hotel physical address */
    address?: string;
  };
}

/**
 * Tenant database configuration
 *
 * In production, this would typically come from a database.
 * For this demo, we define tenants statically to showcase multi-tenant capabilities.
 *
 * Each tenant represents a different hotel with unique:
 * - Branding (colors, logos, messaging)
 * - Feature availability
 * - Contact information
 * - Demo configurations
 */
export const tenants: Record<string, Tenant> = {
  demo: {
    id: 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11',
    name: 'Grand Plaza Hotel',
    subdomain: 'demo',
    branding: {
      primaryColor: '#1e40af',
      secondaryColor: '#f59e0b',
      portalTitle: 'Welcome to Grand Plaza Hotel',
      portalSubtitle: 'Luxury service at your fingertips'
    },
    settings: {
      timezone: 'America/New_York',
      currency: 'USD',
      language: 'en',
      features: {
        maintenance: true,
        housekeeping: true,
        concierge: true,
        announcements: true
      },
      demoConfig: {
        featuredRoom: '237',
        demoRooms: {
          '235': 'Clean',
          '236': 'Occupied',
          '237': 'Maintenance',
          '238': 'Clean',
          '239': 'Occupied',
          '240': 'Housekeeping'
        }
      }
    },
    contact: {
      phone: '+****************',
      email: '<EMAIL>',
      address: '123 Plaza Street, New York, NY 10001'
    }
  },
  parkhotel: {
    id: 'park-hotel-uuid',
    name: 'Park Hotel & Spa',
    subdomain: 'parkhotel',
    customDomain: 'services.parkhotel.com',
    branding: {
      primaryColor: '#059669',
      secondaryColor: '#d97706',
      portalTitle: 'Park Hotel Guest Services',
      portalSubtitle: 'Your comfort is our priority'
    },
    settings: {
      timezone: 'America/Chicago',
      currency: 'USD',
      language: 'en',
      features: {
        maintenance: true,
        housekeeping: true,
        concierge: true,
        announcements: true
      }
    },
    contact: {
      phone: '+****************',
      email: '<EMAIL>',
      address: '456 Park Avenue, Chicago, IL 60601'
    }
  },
  oceanview: {
    id: 'ocean-view-uuid',
    name: 'Ocean View Resort',
    subdomain: 'oceanview',
    branding: {
      primaryColor: '#0891b2',
      secondaryColor: '#f59e0b',
      portalTitle: 'Ocean View Resort Portal',
      portalSubtitle: 'Paradise awaits'
    },
    settings: {
      timezone: 'America/Los_Angeles',
      currency: 'USD',
      language: 'en',
      features: {
        maintenance: true,
        housekeeping: true,
        concierge: true,
        announcements: true
      }
    },
    contact: {
      phone: '+****************',
      email: '<EMAIL>',
      address: '789 Beach Boulevard, San Diego, CA 92101'
    }
  }
};

/**
 * Cache for the current tenant to avoid repeated lookups
 * This improves performance by preventing multiple tenant detection calls
 */
let cachedTenant: Tenant | null = null;

/**
 * Get current tenant based on subdomain or custom domain
 *
 * This function determines which hotel/tenant configuration to use based on:
 * 1. URL parameters (development only)
 * 2. Custom domains (e.g., services.parkhotel.com)
 * 3. Subdomains (e.g., parkhotel.hospitalityflows.com)
 * 4. Fallback to demo tenant
 *
 * @returns {Tenant} The current tenant configuration
 */
export function getCurrentTenant(): Tenant {
  // Return cached tenant if available to avoid repeated processing
  if (cachedTenant) return cachedTenant;

  const hostname = window.location.hostname;

  // Development environment detection
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // Check URL params for tenant override in development
    // Example: http://localhost:3000?tenant=parkhotel
    const urlParams = new URLSearchParams(window.location.search);
    const tenantParam = urlParams.get('tenant');
    if (tenantParam && tenants[tenantParam]) {
      cachedTenant = tenants[tenantParam];
      return cachedTenant;
    }
    // Default to demo tenant in development
    cachedTenant = tenants.demo;
    return cachedTenant;
  }

  // Check if the hostname matches a custom domain
  // Example: services.parkhotel.com
  const customTenant = Object.values(tenants).find(
    tenant => tenant.customDomain === hostname
  );
  if (customTenant) {
    cachedTenant = customTenant;
    return cachedTenant;
  }

  // Check subdomain pattern (e.g., parkhotel.hospitalityflows.com)
  const parts = hostname.split('.');
  if (parts.length >= 3) { // Has subdomain
    const subdomain = parts[0]; // Extract the first part as subdomain
    const tenant = Object.values(tenants).find(
      t => t.subdomain === subdomain
    );
    if (tenant) {
      cachedTenant = tenant;
      return cachedTenant;
    }
  }

  // Fallback to demo tenant if no match found
  console.warn(`No tenant found for hostname: ${hostname}, using demo tenant`);
  cachedTenant = tenants.demo;
  return cachedTenant;
}

/**
 * Clear tenant cache
 *
 * Useful when switching tenants in development or when tenant configuration changes.
 * Forces the next call to getCurrentTenant() to re-evaluate the tenant.
 */
export function clearTenantCache() {
  cachedTenant = null;
}

/**
 * Get tenant by ID
 *
 * @param {string} id - The tenant UUID to search for
 * @returns {Tenant | undefined} The tenant configuration or undefined if not found
 */
export function getTenantById(id: string): Tenant | undefined {
  return Object.values(tenants).find(tenant => tenant.id === id);
}

/**
 * Get all tenants
 *
 * Useful for admin interfaces that need to display or manage multiple tenants.
 *
 * @returns {Tenant[]} Array of all tenant configurations
 */
export function getAllTenants(): Tenant[] {
  return Object.values(tenants);
}

/**
 * Check if a feature is enabled for the current tenant
 *
 * This function provides feature toggle functionality, allowing different
 * hotels to enable/disable specific services based on their needs.
 *
 * @param {FeatureKey} feature - The feature to check (maintenance, housekeeping, etc.)
 * @returns {boolean} True if the feature is enabled, false otherwise
 */
export function isFeatureEnabled(feature: FeatureKey): boolean {
  const tenant = getCurrentTenant();
  // Default to true if feature setting is not specified
  return tenant.settings?.features?.[feature] ?? true;
}

// Legacy exports for backward compatibility
export const DEMO_TENANT_ID = tenants.demo.id;
export const DEMO_SUBDOMAIN = tenants.demo.subdomain;
export const DEFAULT_PORTAL_BRANDING = {
  logo_url: tenants.demo.branding?.logo || null,
  primary_color: tenants.demo.branding?.primaryColor || '#1e40af',
  secondary_color: tenants.demo.branding?.secondaryColor || '#3b82f6',
  portal_title: tenants.demo.branding?.portalTitle || 'Guest Services Portal',
  welcome_message: 'How can we help you today?',
  contact_phone: tenants.demo.contact?.phone || '+****************',
  contact_email: tenants.demo.contact?.email || '<EMAIL>'
};
