import React from 'react';
import { getCurrentTenant } from '../../lib/tenant-config';

const LiveActivityFeed: React.FC = () => {
  const tenant = getCurrentTenant();
  const demoRooms = tenant.settings?.demoConfig?.demoRooms || {};
  const roomNumbers = Object.keys(demoRooms);
  
  return (
    <div className="bg-white rounded-lg shadow-lg">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Live Activity</h2>
      </div>
      <div className="p-6">
        <div className="space-y-3 text-sm">
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-gray-600">Room {roomNumbers[0] || '101'} checkout completed</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
            <span className="text-gray-600">New maintenance request - Room {roomNumbers[2] || '103'}</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
            <span className="text-gray-600">Housekeeping assigned - Room {roomNumbers[5] || '106'}</span>
          </div>
          <div className="flex items-center gap-3">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-gray-600">Guest request resolved - Room {roomNumbers[4] || '105'}</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveActivityFeed;