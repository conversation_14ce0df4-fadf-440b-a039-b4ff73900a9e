import React from 'react';

export interface ServiceConfig {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  buttonColor: string;
  options: string[];
}

interface ServiceCardProps {
  serviceKey: string;
  service: ServiceConfig;
  onClick: () => void;
}

const ServiceCard: React.FC<ServiceCardProps> = ({ serviceKey, service, onClick }) => {
  const getDescription = () => {
    switch (serviceKey) {
      case 'maintenance':
        return 'Report issues with AC, heating, plumbing, electrical, or other room features';
      case 'housekeeping':
        return 'Request fresh towels, toiletries, cleaning, or turndown service';
      case 'concierge':
        return 'Get help with reservations, transportation, and local recommendations';
      default:
        return '';
    }
  };

  return (
    <button
      onClick={onClick}
      className="bg-white rounded-lg shadow-lg p-8 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
    >
      <div className={`w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4 ${service.color}`}>
        <service.icon className="w-8 h-8" />
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">{service.title}</h3>
      <p className="text-gray-600 text-sm">{getDescription()}</p>
    </button>
  );
};

export default ServiceCard;