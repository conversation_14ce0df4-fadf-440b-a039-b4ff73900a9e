# QR Code Implementation Guide

This guide covers the QR code functionality in HospitalityFlows using the latest qrcode.react library.

## Components

### 1. **Basic QR Code** (`RoomQRCode.tsx`)
Simple QR code with room number label.

```tsx
import RoomQRCode from '../components/RoomQRCode';

<RoomQRCode 
  roomNumber="237"
  tenantSubdomain="demo"
  size={160}
  marginSize={4}
  level="H"
/>
```

### 2. **Advanced QR Code** (`AdvancedRoomQRCode.tsx`)
Styled QR codes with hotel branding and logo.

```tsx
import AdvancedRoomQRCode from '../components/AdvancedRoomQRCode';

<AdvancedRoomQRCode 
  roomNumber="237"
  tenantSubdomain="demo"
  hotelName="Grand Plaza Hotel"
  logoUrl="/hotel-logo.png"
  primaryColor="#1e40af"
  style="modern" // 'modern' | 'classic' | 'minimal'
/>
```

### 3. **Printable Room Cards** (`PrintableRoomCard.tsx`)
Complete room cards with QR code, WiFi info, and instructions.

```tsx
import PrintableRoomCard from '../components/PrintableRoomCard';

<PrintableRoomCard
  roomNumber="237"
  tenantSubdomain="demo"
  hotelName="Grand Plaza Hotel"
  hotelLogo="/hotel-logo.png"
  wifiName="GrandPlaza_Guest"
  wifiPassword="Welcome2025"
  frontDeskPhone="Dial 0"
  primaryColor="#1e40af"
/>
```

### 4. **QR Code Generator** (`admin/QRCodeGenerator.tsx`)
Bulk generation tool in the admin panel.

Features:
- Add/remove multiple rooms
- Download individual QR codes as PNG
- Print all QR codes at once
- Pre-populated with demo rooms for demo tenant

## API Changes (qrcode.react v4+)

### Old API (deprecated)
```tsx
import QRCode from 'qrcode.react';

<QRCode
  value={url}
  includeMargin={true}  // deprecated
/>
```

### New API (current)
```tsx
import { QRCodeSVG } from 'qrcode.react';

<QRCodeSVG
  value={url}
  marginSize={4}  // replaces includeMargin
  level="H"       // Error correction: L, M, Q, H
/>
```

## Key Features

### Error Correction Levels
- **L** - Low (~7% correction)
- **M** - Medium (~15% correction)
- **Q** - Quartile (~25% correction)
- **H** - High (~30% correction) - Recommended for printed QR codes

### Logo Embedding
```tsx
imageSettings={{
  src: '/hotel-logo.png',
  height: 40,
  width: 40,
  excavate: true,  // Clears QR modules behind logo
  opacity: 1,
  x: undefined,    // Auto-centers horizontally
  y: undefined     // Auto-centers vertically
}}
```

### Customization Options
- `size` - QR code size in pixels
- `bgColor` - Background color (any CSS color)
- `fgColor` - Foreground color (any CSS color)
- `marginSize` - White space around QR code (in modules)
- `title` - Accessibility title
- `minVersion` - Minimum QR version (1-40)

## Printing Best Practices

1. **Size**: Use at least 200px for printed QR codes
2. **Error Correction**: Use level "H" for printed materials
3. **Contrast**: Ensure high contrast between colors
4. **Margin**: Include adequate white space (marginSize: 4)
5. **Testing**: Always test scan with multiple devices

## Usage Scenarios

### 1. In-Room Display
Place `PrintableRoomCard` in each room with:
- Hotel branding
- QR code for guest services
- WiFi credentials
- Emergency contacts

### 2. Digital Signage
Use `AdvancedRoomQRCode` with "modern" style for:
- Lobby displays
- Elevator screens
- Restaurant table tents

### 3. Key Cards
Use `RoomQRCode` with small size for:
- Back of key cards
- Welcome packets
- Door hangers

### 4. Marketing Materials
Use `AdvancedRoomQRCode` with hotel logo for:
- Brochures
- Business cards
- Website

## Download Implementation

The QR code generator converts SVG to PNG for download:

```typescript
// 1. Get SVG element
const svg = document.querySelector('svg');

// 2. Convert to blob
const svgData = new XMLSerializer().serializeToString(svg);
const svgBlob = new Blob([svgData], { type: 'image/svg+xml' });

// 3. Create image and canvas
const img = new Image();
img.onload = () => {
  canvas.drawImage(img, 0, 0);
  canvas.toBlob(blob => {
    // 4. Trigger download
    const link = document.createElement('a');
    link.download = 'qr-code.png';
    link.href = URL.createObjectURL(blob);
    link.click();
  });
};
img.src = URL.createObjectURL(svgBlob);
```

## Security Considerations

1. **URL Structure**: Always use HTTPS URLs in QR codes
2. **Tenant Isolation**: Each QR includes tenant subdomain
3. **Room Validation**: Validate room numbers server-side
4. **Rate Limiting**: Implement on guest services endpoints

## Next Steps

1. **Analytics**: Track QR code scans per room
2. **Dynamic QR**: Update destinations without reprinting
3. **Batch Export**: ZIP download of all room QR codes
4. **Templates**: Pre-designed card layouts for different hotel types
