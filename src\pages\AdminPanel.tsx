import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>Left, Settings } from 'lucide-react';
import { supabase, GuestAnnouncement } from '../lib/supabase';
import { useToast } from '../contexts/ToastContext';
import { getCurrentTenant } from '../lib/tenant-config';
import DeviceManagement, { Device, Department } from '../components/admin/DeviceManagement';
import AnnouncementManagement, { AnnouncementForm } from '../components/admin/AnnouncementManagement';
import DepartmentOverview from '../components/admin/DepartmentOverview';
import ActiveAnnouncementsSummary from '../components/admin/ActiveAnnouncementsSummary';
import RoutingConfiguration from '../components/admin/RoutingConfiguration';
import QRCodeGenerator from '../components/admin/QRCodeGenerator';

const AdminPanel: React.FC = () => {
  const tenant = getCurrentTenant();
  const [devices, setDevices] = useState<Device[]>([
    { id: 1, name: 'Front Desk Main', phoneNumber: '+****************', department: 'Front Desk', type: 'desk', isActive: true },
    { id: 2, name: 'Housekeeping Supervisor', phoneNumber: '+****************', department: 'Housekeeping', type: 'mobile', isActive: true },
    { id: 3, name: 'Maintenance Team Lead', phoneNumber: '+****************', department: 'Maintenance', type: 'mobile', isActive: true },
    { id: 4, name: 'Concierge Desk', phoneNumber: '+****************', department: 'Concierge', type: 'desk', isActive: true },
    { id: 5, name: 'Security Emergency', phoneNumber: '+****************', department: 'Security', type: 'mobile', isActive: true },
  ]);

  const [departments] = useState<Department[]>([
    { id: 1, name: 'Front Desk', color: 'bg-blue-500' },
    { id: 2, name: 'Housekeeping', color: 'bg-green-500' },
    { id: 3, name: 'Maintenance', color: 'bg-red-500' },
    { id: 4, name: 'Concierge', color: 'bg-amber-500' },
    { id: 5, name: 'Security', color: 'bg-purple-500' },
  ]);

  const [announcements, setAnnouncements] = useState<GuestAnnouncement[]>([]);
  const { showToast } = useToast();

  useEffect(() => {
    fetchAnnouncements();
  }, []);

  const fetchAnnouncements = async () => {
    try {
      const { data, error } = await supabase
        .from('guest_announcements')
        .select('*')
        .eq('tenant_id', tenant.id)
        .order('display_order', { ascending: true })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching announcements:', error);
        showToast('Error loading announcements', 'error');
      } else {
        setAnnouncements(data || []);
      }
    } catch (error) {
      console.error('Error connecting to database:', error);
      showToast('Database connection error', 'error');
    }
  };

  // Device Management Handlers
  const handleUpdateDevice = (device: Device) => {
    setDevices(devices.map(d => d.id === device.id ? device : d));
  };

  const handleAddDevice = (newDevice: Omit<Device, 'id'>) => {
    const device: Device = {
      ...newDevice,
      id: Math.max(...devices.map(d => d.id)) + 1
    };
    setDevices([...devices, device]);
  };

  const handleDeleteDevice = (id: number) => {
    if (confirm('Are you sure you want to delete this device?')) {
      setDevices(devices.filter(device => device.id !== id));
    }
  };

  const toggleDeviceActive = (id: number) => {
    setDevices(devices.map(device => 
      device.id === id ? { ...device, isActive: !device.isActive } : device
    ));
  };

  // Announcement Management Handlers
  const handleSaveAnnouncement = async (form: AnnouncementForm, editingId?: string) => {
    try {
      if (!form.headline || !form.content) {
        showToast('Please fill in headline and content', 'error');
        return;
      }

      const announcementData = {
        tenant_id: tenant.id,
        headline: form.headline,
        content: form.content,
        announcement_type: form.announcement_type,
        background_color: form.background_color,
        text_color: form.text_color,
        start_date: form.start_date,
        end_date: form.end_date || null,
        image_url: form.image_url || null,
        action_url: form.action_url || null,
        action_text: form.action_text || null,
        display_order: editingId ? 
          announcements.find(a => a.id === editingId)?.display_order : 
          announcements.length,
        is_active: true,
        updated_at: new Date().toISOString()
      };

      if (editingId) {
        // Update existing announcement
        const { error } = await supabase
          .from('guest_announcements')
          .update(announcementData)
          .eq('id', editingId);

        if (error) {
          console.error('Error updating announcement:', error);
          showToast('Error updating announcement', 'error');
        } else {
          showToast('Announcement updated successfully!', 'success');
          fetchAnnouncements();
        }
      } else {
        // Create new announcement
        const { error } = await supabase
          .from('guest_announcements')
          .insert([announcementData]);

        if (error) {
          console.error('Error creating announcement:', error);
          showToast('Error creating announcement', 'error');
        } else {
          showToast('Announcement created successfully!', 'success');
          fetchAnnouncements();
        }
      }
    } catch (error) {
      console.error('Database error:', error);
      showToast('Database connection error', 'error');
    }
  };

  const handleDeleteAnnouncement = async (id: string) => {
    if (confirm('Are you sure you want to delete this announcement?')) {
      try {
        const { error } = await supabase
          .from('guest_announcements')
          .delete()
          .eq('id', id);

        if (error) {
          console.error('Error deleting announcement:', error);
          showToast('Error deleting announcement', 'error');
        } else {
          showToast('Announcement deleted successfully!', 'success');
          fetchAnnouncements();
        }
      } catch (error) {
        console.error('Database error:', error);
        showToast('Database connection error', 'error');
      }
    }
  };

  const toggleAnnouncementActive = async (id: string, currentStatus: boolean) => {
    try {
      const { error } = await supabase
        .from('guest_announcements')
        .update({ is_active: !currentStatus })
        .eq('id', id);

      if (error) {
        console.error('Error toggling announcement:', error);
        showToast('Error updating announcement status', 'error');
      } else {
        showToast(`Announcement ${!currentStatus ? 'activated' : 'deactivated'}`, 'success');
        fetchAnnouncements();
      }
    } catch (error) {
      console.error('Database error:', error);
      showToast('Database connection error', 'error');
    }
  };

  const moveAnnouncement = async (id: string, direction: 'up' | 'down') => {
    const index = announcements.findIndex(a => a.id === id);
    if ((direction === 'up' && index === 0) || (direction === 'down' && index === announcements.length - 1)) {
      return;
    }

    const newAnnouncements = [...announcements];
    const swapIndex = direction === 'up' ? index - 1 : index + 1;
    
    // Swap display orders
    const tempOrder = newAnnouncements[index].display_order;
    newAnnouncements[index].display_order = newAnnouncements[swapIndex].display_order;
    newAnnouncements[swapIndex].display_order = tempOrder;

    // Swap positions in array
    [newAnnouncements[index], newAnnouncements[swapIndex]] = [newAnnouncements[swapIndex], newAnnouncements[index]];

    try {
      // Update both announcements in database
      await Promise.all([
        supabase
          .from('guest_announcements')
          .update({ display_order: newAnnouncements[index].display_order })
          .eq('id', newAnnouncements[index].id),
        supabase
          .from('guest_announcements')
          .update({ display_order: newAnnouncements[swapIndex].display_order })
          .eq('id', newAnnouncements[swapIndex].id)
      ]);

      setAnnouncements(newAnnouncements);
      showToast('Announcement order updated', 'success');
    } catch (error) {
      console.error('Error updating order:', error);
      showToast('Error updating announcement order', 'error');
    }
  };

  const getDeviceCountForDepartment = (departmentName: string) => {
    return devices.filter(d => d.department === departmentName && d.isActive).length;
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-blue-900 text-white py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/staff" className="flex items-center gap-2 text-amber-400 hover:text-amber-300 mb-4 transition-colors">
            <ArrowLeft className="w-4 h-4" />
            Back to Staff Dashboard
          </Link>
          <div className="flex items-center gap-3">
            <Settings className="w-8 h-8 text-amber-400" />
            <h1 className="text-3xl font-bold">Admin Panel</h1>
          </div>
          <p className="text-blue-200 mt-2">Manage devices, departments, and guest announcements</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <AnnouncementManagement
              announcements={announcements}
              onSave={handleSaveAnnouncement}
              onDelete={handleDeleteAnnouncement}
              onToggleActive={toggleAnnouncementActive}
              onReorder={moveAnnouncement}
            />

            <DeviceManagement
              devices={devices}
              departments={departments}
              onUpdateDevice={handleUpdateDevice}
              onAddDevice={handleAddDevice}
              onDeleteDevice={handleDeleteDevice}
              onToggleActive={toggleDeviceActive}
            />

            <div className="mt-8">
              <QRCodeGenerator 
                defaultRooms={tenant.settings?.demoConfig?.demoRooms ? Object.keys(tenant.settings.demoConfig.demoRooms) : []}
              />
            </div>
          </div>

          {/* Sidebar */}
          <div>
            <DepartmentOverview 
              departments={departments} 
              deviceCount={getDeviceCountForDepartment} 
            />
            <ActiveAnnouncementsSummary announcements={announcements} />
            <RoutingConfiguration />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPanel;