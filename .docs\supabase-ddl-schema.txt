CREATE SCHEMA IF NOT EXISTS "public";

CREATE  TABLE "public".tenants ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	name                 varchar(255)  NOT NULL  ,
	subdomain            varchar(100)    ,
	subscription_tier    varchar DEFAULT 'starter'::character varying   ,
	is_active            boolean DEFAULT true   ,
	settings             jsonb DEFAULT '{}'::jsonb   ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT tenants_pkey PRIMARY KEY ( id ),
	CONSTRAINT tenants_subdomain_key UNIQUE ( subdomain ) 
 );

CREATE INDEX idx_tenants_subdomain ON "public".tenants USING  btree ( subdomain ) WHERE (is_active = true);

CREATE  TABLE "public".users ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	email                varchar(255)  NOT NULL  ,
	name                 varchar(255)  NOT NULL  ,
	"role"               varchar DEFAULT 'staff'::character varying   ,
	is_active            boolean DEFAULT true   ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT users_pkey PRIMARY KEY ( id ),
	CONSTRAINT users_tenant_id_email_key UNIQUE ( tenant_id, email ) 
 );

CREATE INDEX idx_users_tenant ON "public".users USING  btree ( tenant_id );

CREATE INDEX idx_users_email ON "public".users USING  btree ( email );

CREATE  TABLE "public".workflows ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	workflow_name        varchar(100)  NOT NULL  ,
	workflow_code        varchar(50)  NOT NULL  ,
	trigger_type         varchar(50)  NOT NULL  ,
	configuration        jsonb DEFAULT '{}'::jsonb NOT NULL  ,
	is_active            boolean DEFAULT true   ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT workflows_pkey PRIMARY KEY ( id ),
	CONSTRAINT workflows_tenant_id_workflow_code_key UNIQUE ( tenant_id, workflow_code ) 
 );

CREATE INDEX idx_workflows_tenant ON "public".workflows USING  btree ( tenant_id );

CREATE  TABLE "public".api_credentials ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	service_name         varchar(100)  NOT NULL  ,
	credentials_encrypted text  NOT NULL  ,
	is_active            boolean DEFAULT true   ,
	last_verified_at     timestamptz    ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT api_credentials_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_api_credentials_tenant ON "public".api_credentials USING  btree ( tenant_id );

CREATE  TABLE "public".communication_channels ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	channel_type         varchar(50)  NOT NULL  ,
	channel_identifier   varchar(255)  NOT NULL  ,
	channel_name         varchar(100)    ,
	is_primary           boolean DEFAULT false   ,
	is_active            boolean DEFAULT true   ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	created_at           timestamptz DEFAULT now()   ,
	CONSTRAINT communication_channels_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_communication_channels_tenant ON "public".communication_channels USING  btree ( tenant_id );

CREATE  TABLE "public".departments ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	name                 varchar(100)  NOT NULL  ,
	code                 varchar(20)  NOT NULL  ,
	is_active            boolean DEFAULT true   ,
	created_at           timestamptz DEFAULT now()   ,
	CONSTRAINT departments_pkey PRIMARY KEY ( id ),
	CONSTRAINT departments_tenant_id_code_key UNIQUE ( tenant_id, code ) 
 );

CREATE INDEX idx_departments_tenant ON "public".departments USING  btree ( tenant_id );

CREATE  TABLE "public".devices ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	device_name          varchar(100)  NOT NULL  ,
	device_type          varchar(50)  NOT NULL  ,
	device_identifier    varchar(255)    ,
	is_active            boolean DEFAULT true   ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT devices_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_devices_tenant ON "public".devices USING  btree ( tenant_id );

CREATE  TABLE "public".guest_announcements ( 
	id                   uuid DEFAULT gen_random_uuid() NOT NULL  ,
	headline             text DEFAULT ''::text NOT NULL  ,
	content              text DEFAULT ''::text NOT NULL  ,
	background_color     text DEFAULT '#1e40af'::text NOT NULL  ,
	text_color           text DEFAULT '#ffffff'::text NOT NULL  ,
	is_active            boolean DEFAULT true NOT NULL  ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	tenant_id            uuid  NOT NULL  ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	announcement_type    varchar DEFAULT 'general'::character varying   ,
	display_order        integer DEFAULT 0   ,
	start_date           timestamptz DEFAULT now()   ,
	end_date             timestamptz    ,
	image_url            text    ,
	action_url           text    ,
	action_text          varchar(100)    ,
	CONSTRAINT header_content_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_header_content_tenant ON "public".guest_announcements USING  btree ( tenant_id );

CREATE INDEX idx_guest_announcements_active_dates ON "public".guest_announcements USING  btree ( tenant_id, start_date, end_date ) WHERE (is_active = true);

CREATE  TABLE "public".message_templates ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	template_code        varchar(50)  NOT NULL  ,
	template_name        varchar(100)  NOT NULL  ,
	channel_type         varchar(50)  NOT NULL  ,
	subject              varchar(255)    ,
	body                 text  NOT NULL  ,
	variables            jsonb DEFAULT '[]'::jsonb   ,
	is_active            boolean DEFAULT true   ,
	created_at           timestamptz DEFAULT now()   ,
	CONSTRAINT message_templates_pkey PRIMARY KEY ( id ),
	CONSTRAINT message_templates_tenant_id_template_code_key UNIQUE ( tenant_id, template_code ) 
 );

CREATE INDEX idx_message_templates_tenant ON "public".message_templates USING  btree ( tenant_id );

CREATE  TABLE "public".request_types ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	department_id        uuid  NOT NULL  ,
	name                 varchar(100)  NOT NULL  ,
	code                 varchar(50)  NOT NULL  ,
	priority_level       integer DEFAULT 3   ,
	sla_minutes          integer DEFAULT 120   ,
	is_active            boolean DEFAULT true   ,
	CONSTRAINT request_types_pkey PRIMARY KEY ( id ),
	CONSTRAINT request_types_tenant_id_code_key UNIQUE ( tenant_id, code ) 
 );

CREATE INDEX idx_request_types_tenant ON "public".request_types USING  btree ( tenant_id );

CREATE  TABLE "public".requests ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	request_number       varchar(50)  NOT NULL  ,
	request_type_id      uuid  NOT NULL  ,
	room_number          varchar(20)    ,
	guest_name           varchar(255)    ,
	guest_contact        varchar(255)    ,
	description          text  NOT NULL  ,
	status               varchar DEFAULT 'new'::character varying   ,
	priority_level       integer DEFAULT 3   ,
	"source"             varchar DEFAULT 'guest'::character varying   ,
	assigned_device_id   uuid    ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	created_at           timestamptz DEFAULT now()   ,
	assigned_at          timestamptz    ,
	completed_at         timestamptz    ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT requests_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_requests_tenant ON "public".requests USING  btree ( tenant_id );

CREATE INDEX idx_requests_status ON "public".requests USING  btree ( status );

CREATE INDEX idx_requests_assigned_device ON "public".requests USING  btree ( assigned_device_id );

CREATE INDEX idx_requests_created ON "public".requests USING  btree ( created_at  DESC );

CREATE INDEX idx_requests_number ON "public".requests USING  btree ( request_number );

CREATE UNIQUE INDEX idx_requests_number_tenant ON "public".requests ( tenant_id, request_number );

CREATE  TABLE "public".rooms ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	room_number          varchar(20)  NOT NULL  ,
	room_type            varchar(50)    ,
	floor                varchar(10)    ,
	building             varchar(50)    ,
	status               varchar DEFAULT 'clean'::character varying   ,
	pms_room_id          varchar(100)    ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	created_at           timestamptz DEFAULT now()   ,
	updated_at           timestamptz DEFAULT now()   ,
	CONSTRAINT rooms_pkey PRIMARY KEY ( id ),
	CONSTRAINT rooms_tenant_id_room_number_key UNIQUE ( tenant_id, room_number ) 
 );

CREATE INDEX idx_rooms_tenant ON "public".rooms USING  btree ( tenant_id );

CREATE INDEX idx_rooms_status ON "public".rooms USING  btree ( status );

CREATE  TABLE "public".device_departments ( 
	device_id            uuid  NOT NULL  ,
	department_id        uuid  NOT NULL  ,
	CONSTRAINT device_departments_pkey PRIMARY KEY ( device_id, department_id )
 );

CREATE  TABLE "public".device_sessions ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	device_id            uuid  NOT NULL  ,
	user_id              uuid  NOT NULL  ,
	login_time           timestamptz DEFAULT now()   ,
	logout_time          timestamptz    ,
	is_active            boolean DEFAULT true   ,
	CONSTRAINT device_sessions_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_device_sessions_device ON "public".device_sessions USING  btree ( device_id );

CREATE INDEX idx_device_sessions_user ON "public".device_sessions USING  btree ( user_id );

CREATE INDEX idx_device_sessions_active ON "public".device_sessions USING  btree ( is_active ) WHERE (is_active = true);

CREATE  TABLE "public".message_logs ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	tenant_id            uuid  NOT NULL  ,
	request_id           uuid    ,
	channel_type         varchar(50)  NOT NULL  ,
	direction            varchar(20)  NOT NULL  ,
	from_identifier      varchar(255)    ,
	to_identifier        varchar(255)    ,
	message_content      text    ,
	status               varchar DEFAULT 'pending'::character varying   ,
	metadata             jsonb DEFAULT '{}'::jsonb   ,
	created_at           timestamptz DEFAULT now()   ,
	CONSTRAINT message_logs_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_message_logs_tenant ON "public".message_logs USING  btree ( tenant_id );

CREATE INDEX idx_message_logs_request ON "public".message_logs USING  btree ( request_id );

CREATE  TABLE "public".request_actions ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	request_id           uuid  NOT NULL  ,
	device_id            uuid    ,
	user_id              uuid    ,
	action_type          varchar(50)  NOT NULL  ,
	action_data          jsonb DEFAULT '{}'::jsonb   ,
	created_at           timestamptz DEFAULT now()   ,
	CONSTRAINT request_actions_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_request_actions_request ON "public".request_actions USING  btree ( request_id );

CREATE  TABLE "public".request_assignments ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	request_id           uuid  NOT NULL  ,
	device_id            uuid  NOT NULL  ,
	assigned_by_user_id  uuid    ,
	assigned_at          timestamptz DEFAULT now()   ,
	unassigned_at        timestamptz    ,
	reason               varchar(255)    ,
	CONSTRAINT request_assignments_pkey PRIMARY KEY ( id )
 );

CREATE  TABLE "public".request_attachments ( 
	id                   uuid DEFAULT uuid_generate_v4() NOT NULL  ,
	request_id           uuid  NOT NULL  ,
	file_name            varchar(255)  NOT NULL  ,
	file_url             text  NOT NULL  ,
	file_type            varchar(50)    ,
	file_size            integer    ,
	uploaded_by_user_id  uuid    ,
	created_at           timestamptz DEFAULT now()   ,
	CONSTRAINT request_attachments_pkey PRIMARY KEY ( id )
 );

CREATE INDEX idx_request_attachments_request ON "public".request_attachments USING  btree ( request_id );

ALTER TABLE "public".api_credentials ADD CONSTRAINT api_credentials_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".communication_channels ADD CONSTRAINT communication_channels_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".departments ADD CONSTRAINT departments_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".device_departments ADD CONSTRAINT device_departments_device_id_fkey FOREIGN KEY ( device_id ) REFERENCES "public".devices( id ) ON DELETE CASCADE;

ALTER TABLE "public".device_departments ADD CONSTRAINT device_departments_department_id_fkey FOREIGN KEY ( department_id ) REFERENCES "public".departments( id ) ON DELETE CASCADE;

ALTER TABLE "public".device_sessions ADD CONSTRAINT device_sessions_device_id_fkey FOREIGN KEY ( device_id ) REFERENCES "public".devices( id ) ON DELETE CASCADE;

ALTER TABLE "public".device_sessions ADD CONSTRAINT device_sessions_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".users( id ) ON DELETE CASCADE;

ALTER TABLE "public".devices ADD CONSTRAINT devices_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".guest_announcements ADD CONSTRAINT header_content_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".message_logs ADD CONSTRAINT message_logs_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".message_logs ADD CONSTRAINT message_logs_request_id_fkey FOREIGN KEY ( request_id ) REFERENCES "public".requests( id );

ALTER TABLE "public".message_templates ADD CONSTRAINT message_templates_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".request_actions ADD CONSTRAINT request_actions_request_id_fkey FOREIGN KEY ( request_id ) REFERENCES "public".requests( id ) ON DELETE CASCADE;

ALTER TABLE "public".request_actions ADD CONSTRAINT request_actions_device_id_fkey FOREIGN KEY ( device_id ) REFERENCES "public".devices( id );

ALTER TABLE "public".request_actions ADD CONSTRAINT request_actions_user_id_fkey FOREIGN KEY ( user_id ) REFERENCES "public".users( id );

ALTER TABLE "public".request_assignments ADD CONSTRAINT request_assignments_request_id_fkey FOREIGN KEY ( request_id ) REFERENCES "public".requests( id ) ON DELETE CASCADE;

ALTER TABLE "public".request_assignments ADD CONSTRAINT request_assignments_device_id_fkey FOREIGN KEY ( device_id ) REFERENCES "public".devices( id );

ALTER TABLE "public".request_assignments ADD CONSTRAINT request_assignments_assigned_by_user_id_fkey FOREIGN KEY ( assigned_by_user_id ) REFERENCES "public".users( id );

ALTER TABLE "public".request_attachments ADD CONSTRAINT request_attachments_request_id_fkey FOREIGN KEY ( request_id ) REFERENCES "public".requests( id ) ON DELETE CASCADE;

ALTER TABLE "public".request_attachments ADD CONSTRAINT request_attachments_uploaded_by_user_id_fkey FOREIGN KEY ( uploaded_by_user_id ) REFERENCES "public".users( id );

ALTER TABLE "public".request_types ADD CONSTRAINT request_types_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".request_types ADD CONSTRAINT request_types_department_id_fkey FOREIGN KEY ( department_id ) REFERENCES "public".departments( id ) ON DELETE CASCADE;

ALTER TABLE "public".requests ADD CONSTRAINT requests_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".requests ADD CONSTRAINT requests_request_type_id_fkey FOREIGN KEY ( request_type_id ) REFERENCES "public".request_types( id );

ALTER TABLE "public".requests ADD CONSTRAINT requests_assigned_device_id_fkey FOREIGN KEY ( assigned_device_id ) REFERENCES "public".devices( id );

ALTER TABLE "public".rooms ADD CONSTRAINT rooms_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".users ADD CONSTRAINT users_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

ALTER TABLE "public".workflows ADD CONSTRAINT workflows_tenant_id_fkey FOREIGN KEY ( tenant_id ) REFERENCES "public".tenants( id ) ON DELETE CASCADE;

CREATE OR REPLACE VIEW active_guest_announcements AS SELECT "public".active_guest_announcements,
    ga.headline,
    ga.content,
    ga.background_color,
    ga.text_color,
    ga.is_active,
    ga.created_at,
    ga.updated_at,
    ga.tenant_id,
    ga.metadata,
    ga.announcement_type,
    ga.display_order,
    ga.start_date,
    ga.end_date,
    ga.image_url,
    ga.action_url,
    ga.action_text,
    t.name AS tenant_name,
    t.subdomain
   FROM (guest_announcements ga
     JOIN tenants t ON ((ga.tenant_id = t.id)))
  WHERE ((ga.is_active = true) AND (ga.start_date <= now()) AND ((ga.end_date IS NULL) OR (ga.end_date > now())))
  ORDER BY ga.display_order, ga.created_at DESC;

CREATE OR REPLACE VIEW announcement_management AS SELECT "public".announcement_management,
    ga.tenant_id,
    t.name AS tenant_name,
    ga.headline,
    ga.content,
    ga.announcement_type,
    ga.display_order,
    ga.is_active,
    ga.start_date,
    ga.end_date,
        CASE
            WHEN (ga.is_active = false) THEN 'Inactive'::text
            WHEN (now() < ga.start_date) THEN 'Scheduled'::text
            WHEN ((ga.end_date IS NOT NULL) AND (now() > ga.end_date)) THEN 'Expired'::text
            ELSE 'Active'::text
        END AS status,
    ga.created_at,
    ga.updated_at
   FROM (guest_announcements ga
     JOIN tenants t ON ((ga.tenant_id = t.id)))
  ORDER BY ga.tenant_id, ga.display_order, ga.created_at DESC;

CREATE TRIGGER end_device_sessions_on_login AFTER INSERT ON public.device_sessions FOR EACH ROW EXECUTE FUNCTION end_active_device_sessions();

CREATE TRIGGER track_request_status_changes BEFORE UPDATE ON public.requests FOR EACH ROW EXECUTE FUNCTION log_request_status_change();

CREATE TRIGGER update_api_credentials_updated_at BEFORE UPDATE ON public.api_credentials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_devices_updated_at BEFORE UPDATE ON public.devices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_header_content_updated_at BEFORE UPDATE ON public.guest_announcements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_requests_updated_at BEFORE UPDATE ON public.requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON public.rooms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON public.tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON public.workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE OR REPLACE FUNCTION public.create_announcement(p_tenant_id uuid, p_headline text, p_content text, p_type character varying DEFAULT 'general'::character varying, p_options jsonb DEFAULT '{}'::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_announcement_id UUID;
    v_max_order INTEGER;
BEGIN
    -- Get the max display order for this tenant
    SELECT COALESCE(MAX(display_order), -1) + 1
    INTO v_max_order
    FROM guest_announcements
    WHERE tenant_id = p_tenant_id;
    
    INSERT INTO guest_announcements (
        tenant_id,
        headline,
        content,
        announcement_type,
        background_color,
        text_color,
        image_url,
        action_url,
        action_text,
        display_order,
        start_date,
        end_date,
        metadata
    ) VALUES (
        p_tenant_id,
        p_headline,
        p_content,
        p_type,
        COALESCE(p_options->>'background_color', 
            CASE p_type
                WHEN 'alert' THEN '#dc2626'
                WHEN 'dining' THEN '#059669'
                WHEN 'event' THEN '#1e3a8a'
                WHEN 'promotion' THEN '#7c3aed'
                ELSE '#3b82f6'
            END
        ),
        COALESCE(p_options->>'text_color', '#ffffff'),
        p_options->>'image_url',
        p_options->>'action_url',
        p_options->>'action_text',
        COALESCE((p_options->>'display_order')::INTEGER, v_max_order),
        COALESCE((p_options->>'start_date')::TIMESTAMPTZ, NOW()),
        (p_options->>'end_date')::TIMESTAMPTZ,
        COALESCE(p_options->'metadata', '{}')::jsonb
    ) RETURNING id INTO v_announcement_id;
    
    RETURN v_announcement_id;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.end_active_device_sessions()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- End any active sessions for this device
    UPDATE device_sessions
    SET is_active = false,
        logout_time = NOW()
    WHERE device_id = NEW.device_id
    AND is_active = true
    AND id != NEW.id;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.generate_request_number(tenant_uuid uuid)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
DECLARE
    current_year VARCHAR(4);
    last_number INTEGER;
    new_number VARCHAR(50);
BEGIN
    current_year := TO_CHAR(CURRENT_DATE, 'YYYY');
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(request_number FROM '\d+$') AS INTEGER)), 0)
    INTO last_number
    FROM requests
    WHERE tenant_id = tenant_uuid
    AND request_number LIKE 'REQ-' || current_year || '-%';
    
    new_number := 'REQ-' || current_year || '-' || LPAD((last_number + 1)::TEXT, 4, '0');
    
    RETURN new_number;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_active_announcements(p_tenant_id uuid, p_announcement_type character varying DEFAULT NULL::character varying)
 RETURNS TABLE(id uuid, headline text, content text, announcement_type character varying, background_color text, text_color text, image_url text, action_url text, action_text character varying, display_order integer, metadata jsonb)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        ga.id,
        ga.headline,
        ga.content,
        ga.announcement_type,
        ga.background_color,
        ga.text_color,
        ga.image_url,
        ga.action_url,
        ga.action_text,
        ga.display_order,
        ga.metadata
    FROM guest_announcements ga
    WHERE ga.tenant_id = p_tenant_id
    AND ga.is_active = true
    AND ga.start_date <= NOW()
    AND (ga.end_date IS NULL OR ga.end_date > NOW())
    AND (p_announcement_type IS NULL OR ga.announcement_type = p_announcement_type)
    ORDER BY ga.display_order, ga.created_at DESC;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_active_header_content(p_tenant_id uuid, p_page_type character varying DEFAULT 'guest_portal'::character varying)
 RETURNS TABLE(id uuid, title text, subtitle text, background_color text, text_color text, logo_url text, hero_image_url text, contact_phone character varying, contact_email character varying, portal_instructions text, custom_css text, metadata jsonb)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        hc.id,
        hc.title,
        hc.subtitle,
        hc.background_color,
        hc.text_color,
        hc.logo_url,
        hc.hero_image_url,
        hc.contact_phone,
        hc.contact_email,
        hc.portal_instructions,
        hc.custom_css,
        hc.metadata
    FROM header_content hc
    WHERE hc.tenant_id = p_tenant_id
    AND hc.page_type = p_page_type
    AND hc.is_active = true
    LIMIT 1;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_complete_guest_portal(p_subdomain character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_tenant_id UUID;
    v_result JSONB;
BEGIN
    -- Get tenant ID from subdomain
    SELECT id INTO v_tenant_id
    FROM tenants
    WHERE subdomain = p_subdomain
    AND is_active = true;
    
    IF v_tenant_id IS NULL THEN
        RETURN jsonb_build_object('error', 'Tenant not found');
    END IF;
    
    -- Build complete portal response
    SELECT jsonb_build_object(
        'tenant', jsonb_build_object(
            'id', t.id,
            'name', t.name,
            'branding', COALESCE(t.settings->'portal_branding', '{}')
        ),
        'announcements', (
            SELECT COALESCE(jsonb_agg(
                jsonb_build_object(
                    'id', a.id,
                    'headline', a.headline,
                    'content', a.content,
                    'type', a.announcement_type,
                    'backgroundColor', a.background_color,
                    'textColor', a.text_color,
                    'imageUrl', a.image_url,
                    'actionUrl', a.action_url,
                    'actionText', a.action_text,
                    'metadata', a.metadata
                ) ORDER BY a.display_order
            ), '[]'::jsonb)
            FROM get_active_announcements(t.id) a
        ),
        'requestTypes', (
            SELECT COALESCE(jsonb_agg(
                jsonb_build_object(
                    'id', rt.id,
                    'name', rt.name,
                    'code', rt.code,
                    'department', d.name,
                    'departmentId', d.id,
                    'priority', rt.priority_level,
                    'slaMinutes', rt.sla_minutes
                ) ORDER BY rt.priority_level, rt.name
            ), '[]'::jsonb)
            FROM request_types rt
            JOIN departments d ON rt.department_id = d.id
            WHERE rt.tenant_id = t.id
            AND rt.is_active = true
            AND d.is_active = true
        ),
        'communicationChannels', (
            SELECT COALESCE(jsonb_agg(
                jsonb_build_object(
                    'type', cc.channel_type,
                    'identifier', cc.channel_identifier,
                    'name', cc.channel_name,
                    'isPrimary', cc.is_primary
                ) ORDER BY cc.is_primary DESC, cc.channel_type
            ), '[]'::jsonb)
            FROM communication_channels cc
            WHERE cc.tenant_id = t.id
            AND cc.is_active = true
        )
    ) INTO v_result
    FROM tenants t
    WHERE t.id = v_tenant_id;
    
    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_current_tenant_id()
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- This will be set by your application when connecting
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_guest_portal_content(p_tenant_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'announcements', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', a.id,
                    'headline', a.headline,
                    'content', a.content,
                    'type', a.announcement_type,
                    'backgroundColor', a.background_color,
                    'textColor', a.text_color,
                    'imageUrl', a.image_url,
                    'actionUrl', a.action_url,
                    'actionText', a.action_text,
                    'metadata', a.metadata
                ) ORDER BY a.display_order, a.created_at DESC
            )
            FROM get_active_announcements(p_tenant_id) a
        ),
        'request_types', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', rt.id,
                    'name', rt.name,
                    'code', rt.code,
                    'department', d.name,
                    'priority', rt.priority_level
                ) ORDER BY rt.priority_level, rt.name
            )
            FROM request_types rt
            JOIN departments d ON rt.department_id = d.id
            WHERE rt.tenant_id = p_tenant_id
            AND rt.is_active = true
        )
    ) INTO v_result;
    
    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.get_guest_portal_data(p_tenant_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'header', (
            SELECT row_to_json(hc.*)
            FROM get_active_header_content(p_tenant_id, 'guest_portal') hc
            LIMIT 1
        ),
        'request_types', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', rt.id,
                    'name', rt.name,
                    'code', rt.code,
                    'department', d.name,
                    'priority_level', rt.priority_level
                ) ORDER BY rt.priority_level, rt.name
            )
            FROM request_types rt
            JOIN departments d ON rt.department_id = d.id
            WHERE rt.tenant_id = p_tenant_id
            AND rt.is_active = true
        ),
        'communication_channels', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'type', cc.channel_type,
                    'identifier', cc.channel_identifier,
                    'is_primary', cc.is_primary
                )
            )
            FROM communication_channels cc
            WHERE cc.tenant_id = p_tenant_id
            AND cc.is_active = true
        )
    ) INTO v_result;
    
    RETURN v_result;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.log_request_status_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO request_actions (
            request_id,
            action_type,
            action_data
        ) VALUES (
            NEW.id,
            'status_changed',
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status
            )
        );
    END IF;
    
    -- Update timestamp fields based on status
    IF NEW.status = 'assigned' AND OLD.status != 'assigned' THEN
        NEW.assigned_at = NOW();
    ELSIF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.reorder_announcements(p_tenant_id uuid, p_announcement_ids uuid[])
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_id UUID;
    v_order INTEGER := 0;
BEGIN
    FOREACH v_id IN ARRAY p_announcement_ids
    LOOP
        UPDATE guest_announcements
        SET display_order = v_order
        WHERE id = v_id 
        AND tenant_id = p_tenant_id;
        
        v_order := v_order + 1;
    END LOOP;
    
    RETURN TRUE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.upsert_header_content(p_tenant_id uuid, p_page_type character varying, p_data jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_header_id UUID;
BEGIN
    -- Deactivate existing active headers for this tenant/page_type
    UPDATE header_content
    SET is_active = false
    WHERE tenant_id = p_tenant_id
    AND page_type = p_page_type
    AND is_active = true;
    
    -- Insert new header content
    INSERT INTO header_content (
        tenant_id,
        page_type,
        title,
        subtitle,
        background_color,
        text_color,
        logo_url,
        hero_image_url,
        contact_phone,
        contact_email,
        portal_instructions,
        custom_css,
        metadata,
        is_active
    ) VALUES (
        p_tenant_id,
        p_page_type,
        COALESCE(p_data->>'title', 'Welcome'),
        COALESCE(p_data->>'subtitle', ''),
        COALESCE(p_data->>'background_color', '#1e40af'),
        COALESCE(p_data->>'text_color', '#ffffff'),
        p_data->>'logo_url',
        p_data->>'hero_image_url',
        p_data->>'contact_phone',
        p_data->>'contact_email',
        p_data->>'portal_instructions',
        p_data->>'custom_css',
        COALESCE(p_data->'metadata', '{}')::jsonb,
        true
    ) RETURNING id INTO v_header_id;
    
    RETURN v_header_id;
END;
$function$
;

COMMENT ON TABLE "public".guest_announcements IS 'Stores dynamic content/announcements that hotels can display on their guest portal. 
Examples: dinner specials, events, pool closures, happy hour promotions, etc.';

COMMENT ON FUNCTION "public".create_announcement IS 'Create a new guest announcement. Example usage:
SELECT create_announcement(
    ''tenant-uuid'',
    ''Tonight: Italian Night Buffet'',
    ''All-you-can-eat Italian buffet featuring homemade pasta, pizza, and tiramisu. $29.99 per person.'',
    ''dining'',
    ''{
        "action_text": "Reserve Now",
        "action_url": "/reservations",
        "end_date": "2024-12-25T23:00:00Z",
        "metadata": {"price": "$29.99", "location": "Main Restaurant"}
    }''::jsonb
);';

COMMENT ON FUNCTION "public".get_active_announcements IS 'Get active announcements for display. Examples:
-- Get all active announcements
SELECT * FROM get_active_announcements(''tenant-uuid'');

-- Get only dining announcements
SELECT * FROM get_active_announcements(''tenant-uuid'', ''dining'');

-- Get only alerts
SELECT * FROM get_active_announcements(''tenant-uuid'', ''alert'');';

COMMENT ON FUNCTION "public".get_complete_guest_portal IS 'Returns complete guest portal data including tenant branding, active announcements, request types, and communication channels. 
Usage: SELECT get_complete_guest_portal(''demo'');';

