# Cloudflare Pages Deployment Configuration

This project is configured for deployment on Cloudflare Pages.

## Configuration Files

1. **`public/_redirects`** - Handles SPA routing by directing all routes to index.html
2. **`public/_headers`** - Sets security headers for all routes

## Build Settings for Cloudflare Pages

When deploying to Cloudflare Pages, use these settings:

- **Build command**: `npm run build`
- **Build output directory**: `dist`
- **Node version**: 18 or higher (set in environment variables if needed)

## Environment Variables

Make sure to set these environment variables in your Cloudflare Pages dashboard:

- `VITE_SUPABASE_URL` - Your Supabase project URL
- `VITE_SUPABASE_ANON_KEY` - Your Supabase anonymous key

## Deployment Steps

1. Connect your GitHub repository to Cloudflare Pages
2. Set the build configuration as specified above
3. Add the environment variables
4. Deploy!

## Notes

- The `vercel.json` file has been removed as it's not needed for Cloudflare Pages
- All configuration is handled through the `_redirects` and `_headers` files
- These files are automatically included in the build output from the `public` directory