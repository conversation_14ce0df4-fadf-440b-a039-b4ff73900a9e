import React from 'react';
import { Clock, CheckCircle, AlertCircle, TrendingUp } from 'lucide-react';

interface StatsData {
  pendingCount: number;
  completedCount: number;
}

interface StaffStatsProps {
  stats: StatsData;
}

const StaffStats: React.FC<StaffStatsProps> = ({ stats }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center">
          <Clock className="w-8 h-8 text-blue-600 mr-3" />
          <div>
            <p className="text-sm text-gray-600">Avg Response Time</p>
            <p className="text-2xl font-bold text-gray-900">45s</p>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center">
          <AlertCircle className="w-8 h-8 text-red-600 mr-3" />
          <div>
            <p className="text-sm text-gray-600">Pending Requests</p>
            <p className="text-2xl font-bold text-gray-900">{stats.pendingCount}</p>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center">
          <CheckCircle className="w-8 h-8 text-green-600 mr-3" />
          <div>
            <p className="text-sm text-gray-600">Completed Today</p>
            <p className="text-2xl font-bold text-gray-900">{stats.completedCount}</p>
          </div>
        </div>
      </div>
      
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="flex items-center">
          <TrendingUp className="w-8 h-8 text-amber-600 mr-3" />
          <div>
            <p className="text-sm text-gray-600">Efficiency Score</p>
            <p className="text-2xl font-bold text-gray-900">98%</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StaffStats;