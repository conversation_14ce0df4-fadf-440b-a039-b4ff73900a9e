# API Documentation

## Overview

HospitalityFlows uses Supabase as its backend-as-a-service, providing a PostgreSQL database with real-time capabilities, authentication, and row-level security (RLS). This document outlines the database schema, API patterns, and integration points.

## Database Architecture

### Multi-Tenant Design

All tables include a `tenant_id` column to ensure data isolation between hotels. Row Level Security (RLS) policies enforce tenant-specific access controls.

### Core Tables

#### `tenants`
Stores hotel/tenant configuration and settings.

```sql
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  subdomain VARCHAR(100) UNIQUE,
  subscription_tier VARCHAR DEFAULT 'starter',
  is_active BOOLEAN DEFAULT true,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Key Fields:**
- `id`: Unique tenant identifier
- `subdomain`: Used for multi-tenant routing
- `settings`: JSON configuration for features, branding, etc.

#### `guest_announcements`
Manages announcements displayed to guests.

```sql
CREATE TABLE guest_announcements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  headline TEXT NOT NULL,
  content TEXT NOT NULL,
  announcement_type VARCHAR(50),
  background_color TEXT DEFAULT '#1e40af',
  text_color TEXT DEFAULT '#ffffff',
  is_active BOOLEAN DEFAULT true,
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  display_order INTEGER,
  image_url TEXT,
  action_url TEXT,
  action_text TEXT,
  metadata JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Key Features:**
- Scheduled announcements with start/end dates
- Custom styling with colors and images
- Display ordering for multiple announcements
- Action buttons with custom URLs

#### `requests`
Stores guest service requests.

```sql
CREATE TABLE requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  request_number VARCHAR(50) NOT NULL,
  request_type_id UUID REFERENCES request_types(id),
  room_number VARCHAR(20),
  guest_name VARCHAR(255),
  guest_contact VARCHAR(255),
  description TEXT NOT NULL,
  status VARCHAR DEFAULT 'new',
  priority_level INTEGER DEFAULT 3,
  source VARCHAR DEFAULT 'guest',
  assigned_device_id UUID,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  assigned_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Status Values:**
- `new`: Just created
- `assigned`: Assigned to staff
- `in_progress`: Being worked on
- `completed`: Finished
- `cancelled`: Cancelled

#### `request_types`
Defines available service categories.

```sql
CREATE TABLE request_types (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  code VARCHAR(50) NOT NULL,
  name VARCHAR(100) NOT NULL,
  department_id UUID REFERENCES departments(id),
  priority_level INTEGER DEFAULT 3,
  sla_minutes INTEGER,
  is_active BOOLEAN DEFAULT true
);
```

**Common Types:**
- `maintenance`: Maintenance requests
- `housekeeping`: Housekeeping services
- `concierge`: Concierge services

#### `departments`
Organizes staff and routing.

```sql
CREATE TABLE departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  name VARCHAR(100) NOT NULL,
  code VARCHAR(50) NOT NULL,
  webhook_url TEXT,
  is_active BOOLEAN DEFAULT true
);
```

#### `rooms`
Hotel room inventory.

```sql
CREATE TABLE rooms (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  room_number VARCHAR(20) NOT NULL,
  room_type VARCHAR(50),
  floor VARCHAR(10),
  building VARCHAR(50),
  status VARCHAR DEFAULT 'clean',
  pms_room_id VARCHAR(100),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

**Room Status Values:**
- `clean`: Ready for guests
- `dirty`: Needs cleaning
- `occupied`: Guest checked in
- `maintenance`: Under maintenance
- `out_of_order`: Not available

## Database Functions

### `get_active_announcements(p_tenant_id, p_announcement_type?)`
Retrieves active announcements for a tenant.

```sql
SELECT * FROM get_active_announcements('tenant-uuid', 'general');
```

### `create_announcement(p_tenant_id, p_headline, p_content, p_type?, p_options?)`
Creates a new announcement.

```sql
SELECT create_announcement(
  'tenant-uuid',
  'Welcome Message',
  'Welcome to our hotel!',
  'general',
  '{"background_color": "#1e40af"}'::jsonb
);
```

### `generate_request_number(tenant_uuid)`
Generates unique request numbers.

```sql
SELECT generate_request_number('tenant-uuid');
-- Returns: REQ-20250101-001
```

## API Patterns

### Supabase Client Usage

```typescript
import { supabase } from '../lib/supabase';
import { getCurrentTenant } from '../lib/tenant-config';

// Get current tenant
const tenant = getCurrentTenant();

// Fetch tenant-specific data
const { data, error } = await supabase
  .from('guest_announcements')
  .select('*')
  .eq('tenant_id', tenant.id)
  .eq('is_active', true)
  .order('display_order', { ascending: true });
```

### Common Query Patterns

#### Fetch Active Announcements
```typescript
const fetchAnnouncements = async () => {
  const tenant = getCurrentTenant();
  const { data, error } = await supabase
    .from('guest_announcements')
    .select('*')
    .eq('tenant_id', tenant.id)
    .eq('is_active', true)
    .or(`start_date.is.null,start_date.lte.${new Date().toISOString()}`)
    .or(`end_date.is.null,end_date.gte.${new Date().toISOString()}`)
    .order('display_order', { ascending: true })
    .order('created_at', { ascending: false });

  return data || [];
};
```

#### Create Service Request
```typescript
const createRequest = async (requestData: {
  room_number: string;
  service_type: string;
  description: string;
  guest_name?: string;
  guest_contact?: string;
}) => {
  const tenant = getCurrentTenant();

  const { data, error } = await supabase
    .from('requests')
    .insert({
      tenant_id: tenant.id,
      request_number: `REQ-${Date.now()}`,
      request_type_id: requestData.service_type,
      room_number: requestData.room_number,
      description: requestData.description,
      guest_name: requestData.guest_name,
      guest_contact: requestData.guest_contact,
      status: 'new',
      source: 'guest'
    })
    .select()
    .single();

  return data;
};
```

#### Update Request Status
```typescript
const updateRequestStatus = async (requestId: string, status: string) => {
  const { data, error } = await supabase
    .from('requests')
    .update({
      status,
      updated_at: new Date().toISOString(),
      ...(status === 'completed' && { completed_at: new Date().toISOString() })
    })
    .eq('id', requestId)
    .select()
    .single();

  return data;
};
```

## Row Level Security (RLS)

### Tenant Isolation Policy
```sql
-- Example policy for guest_announcements
CREATE POLICY "Tenant Isolation"
ON guest_announcements
FOR ALL
USING (tenant_id = current_setting('app.current_tenant_id')::uuid);
```

### Public Read Access
```sql
-- Allow public read access to active announcements
CREATE POLICY "Public Read Active Announcements"
ON guest_announcements
FOR SELECT
USING (is_active = true);
```

## Real-time Subscriptions

### Subscribe to Request Updates
```typescript
const subscribeToRequests = (tenantId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('requests')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'requests',
        filter: `tenant_id=eq.${tenantId}`
      },
      callback
    )
    .subscribe();
};
```

### Subscribe to Announcements
```typescript
const subscribeToAnnouncements = (tenantId: string, callback: (payload: any) => void) => {
  return supabase
    .channel('announcements')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'guest_announcements',
        filter: `tenant_id=eq.${tenantId}`
      },
      callback
    )
    .subscribe();
};
```

## Error Handling

### Standard Error Response
```typescript
interface SupabaseError {
  message: string;
  details: string;
  hint: string;
  code: string;
}

const handleSupabaseError = (error: SupabaseError) => {
  console.error('Supabase error:', error);

  switch (error.code) {
    case '23505': // Unique violation
      return 'This record already exists';
    case '23503': // Foreign key violation
      return 'Referenced record not found';
    case '42501': // Insufficient privilege
      return 'Access denied';
    default:
      return 'An unexpected error occurred';
  }
};
```

## TypeScript Types

### Generated Types
The application uses generated TypeScript types from Supabase:

```typescript
import { Database } from '../lib/supabase';

// Table row types
type GuestAnnouncement = Database['public']['Tables']['guest_announcements']['Row'];
type Request = Database['public']['Tables']['requests']['Row'];
type Tenant = Database['public']['Tables']['tenants']['Row'];

// Insert types (for creating records)
type NewAnnouncement = Database['public']['Tables']['guest_announcements']['Insert'];
type NewRequest = Database['public']['Tables']['requests']['Insert'];

// Update types (for updating records)
type AnnouncementUpdate = Database['public']['Tables']['guest_announcements']['Update'];
type RequestUpdate = Database['public']['Tables']['requests']['Update'];
```

### Custom Types
```typescript
// Service request form data
interface ServiceRequestForm {
  service_type: string;
  description: string;
  urgency: 'low' | 'medium' | 'high';
  guest_name?: string;
  guest_contact?: string;
}

// Announcement form data
interface AnnouncementForm {
  headline: string;
  content: string;
  type: string;
  background_color: string;
  text_color: string;
  start_date?: string;
  end_date?: string;
  action_url?: string;
  action_text?: string;
}
```

## Integration Points

### n8n Webhook Integration
Service requests trigger webhooks to n8n for workflow automation:

```typescript
const sendWebhook = async (requestData: any) => {
  const webhookUrl = import.meta.env.VITE_N8N_WEBHOOK_URL;

  if (!webhookUrl) return;

  const payload = {
    tenant_id: requestData.tenant_id,
    tenant_subdomain: getCurrentTenant().subdomain,
    hotel_name: getCurrentTenant().name,
    room: requestData.room_number,
    service: requestData.service_type,
    description: requestData.description,
    urgency: requestData.priority_level,
    guest_name: requestData.guest_name,
    guest_contact: requestData.guest_contact,
    timestamp: new Date().toISOString()
  };

  try {
    await fetch(webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });
  } catch (error) {
    console.error('Webhook error:', error);
  }
};
```

### PMS Integration
Future integration points for Property Management Systems:

```typescript
interface PMSIntegration {
  // Guest information lookup
  getGuestInfo(roomNumber: string): Promise<GuestInfo>;

  // Room status updates
  updateRoomStatus(roomNumber: string, status: string): Promise<void>;

  // Billing integration
  addCharges(roomNumber: string, charges: Charge[]): Promise<void>;
}
```

## Performance Considerations

### Indexing Strategy
```sql
-- Tenant-based queries
CREATE INDEX idx_guest_announcements_tenant_active
ON guest_announcements(tenant_id, is_active);

-- Request queries
CREATE INDEX idx_requests_tenant_status
ON requests(tenant_id, status, created_at);

-- Room lookups
CREATE INDEX idx_rooms_tenant_number
ON rooms(tenant_id, room_number);
```

### Query Optimization
- Always filter by `tenant_id` first
- Use appropriate indexes for common query patterns
- Limit result sets with pagination
- Use select() to specify only needed columns

### Caching Strategy
- Cache tenant configurations in memory
- Use browser storage for user preferences
- Implement optimistic updates for better UX

## Security Best Practices

### Input Validation
```typescript
const validateRoomNumber = (roomNumber: string): boolean => {
  return /^[A-Z0-9-]{1,20}$/i.test(roomNumber);
};

const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};
```

### SQL Injection Prevention
- Always use parameterized queries
- Validate input types and formats
- Use Supabase's built-in sanitization

### Access Control
- Implement RLS policies on all tables
- Validate tenant access in application logic
- Use environment variables for sensitive configuration

## Monitoring and Logging

### Database Monitoring
- Track query performance
- Monitor connection pool usage
- Set up alerts for error rates

### Application Logging
```typescript
const logActivity = async (activity: {
  tenant_id: string;
  action: string;
  resource: string;
  metadata?: any;
}) => {
  await supabase
    .from('activity_logs')
    .insert({
      ...activity,
      timestamp: new Date().toISOString()
    });
};
```

## Migration Strategy

### Schema Changes
1. Create migration files in `supabase/migrations/`
2. Test migrations in development
3. Apply to staging environment
4. Deploy to production with rollback plan

### Data Migration
```sql
-- Example: Adding new column with default value
ALTER TABLE requests
ADD COLUMN priority_score INTEGER DEFAULT 0;

-- Update existing records
UPDATE requests
SET priority_score = CASE
  WHEN priority_level = 1 THEN 100
  WHEN priority_level = 2 THEN 75
  WHEN priority_level = 3 THEN 50
  ELSE 25
END;
```

## Troubleshooting

### Common Issues

#### RLS Policy Errors
```
Error: new row violates row-level security policy
```
**Solution:** Ensure tenant_id is properly set and RLS policies allow the operation.

#### Connection Timeouts
```
Error: Connection timeout
```
**Solution:** Check network connectivity and Supabase service status.

#### Type Errors
```
Error: Type 'string' is not assignable to type 'UUID'
```
**Solution:** Validate UUID format and use proper type casting.

### Debug Queries
```typescript
// Enable query logging in development
if (import.meta.env.DEV) {
  supabase.auth.onAuthStateChange((event, session) => {
    console.log('Auth event:', event, session);
  });
}
```

## API Rate Limits

### Supabase Limits
- Free tier: 500 MB database, 2 GB bandwidth
- Pro tier: 8 GB database, 250 GB bandwidth
- Enterprise: Custom limits

### Best Practices
- Implement client-side caching
- Use pagination for large datasets
- Batch operations when possible
- Monitor usage in Supabase dashboard

## Backup and Recovery

### Automated Backups
Supabase provides automated daily backups for Pro+ plans.

### Manual Backup
```bash
# Export schema
pg_dump --schema-only --no-owner --no-privileges $DATABASE_URL > schema.sql

# Export data
pg_dump --data-only --no-owner --no-privileges $DATABASE_URL > data.sql
```

### Point-in-Time Recovery
Available for Pro+ plans with up to 7 days retention.

## Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Multi-Tenant Guide](./MULTI_TENANT_GUIDE.md)
- [QR Code Implementation](./QR_CODE_IMPLEMENTATION.md)

---

For questions about the API or database schema, please refer to the source code in `src/lib/supabase.ts` or contact the development team.
