import React from 'react';
import { Phone, Mail, MessageSquare } from 'lucide-react';
import { ServiceConfig } from './ServiceCard';
import GuestInfoSection from './GuestInfoSection';

export interface RequestForm {
  type: string;
  description: string;
  urgency: 'Low' | 'Medium' | 'High';
  contact: 'call' | 'text' | 'email';
  phone: string;
  email: string;
}

interface ServiceRequestFormProps {
  service: ServiceConfig;
  form: RequestForm;
  isEditingPhone: boolean;
  isEditingEmail: boolean;
  onFormChange: (updates: Partial<RequestForm>) => void;
  onEditPhone: () => void;
  onEditEmail: () => void;
  onPhoneEditComplete: () => void;
  onEmailEditComplete: () => void;
  onSubmit: (e: React.FormEvent) => void;
}

const ServiceRequestForm: React.FC<ServiceRequestFormProps> = ({
  service,
  form,
  isEditingPhone,
  isEditingEmail,
  onFormChange,
  onEditPhone,
  onEditEmail,
  onPhoneEditComplete,
  onEmailEditComplete,
  onSubmit
}) => {
  return (
    <form onSubmit={onSubmit} className="bg-white rounded-lg shadow-lg p-8">
      <GuestInfoSection
        phone={form.phone}
        email={form.email}
        isEditingPhone={isEditingPhone}
        isEditingEmail={isEditingEmail}
        onPhoneChange={(value) => onFormChange({ phone: value })}
        onEmailChange={(value) => onFormChange({ email: value })}
        onEditPhone={onEditPhone}
        onEditEmail={onEditEmail}
        onPhoneEditComplete={onPhoneEditComplete}
        onEmailEditComplete={onEmailEditComplete}
      />

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Request Type
        </label>
        <select
          value={form.type}
          onChange={(e) => onFormChange({ type: e.target.value })}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        >
          <option value="">Select type...</option>
          {service.options.map(option => (
            <option key={option} value={option}>{option}</option>
          ))}
        </select>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Description
        </label>
        <textarea
          value={form.description}
          onChange={(e) => onFormChange({ description: e.target.value })}
          rows={4}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Please provide details about your request..."
          required
        />
        <p className="text-xs text-gray-500 mt-2">
          Please keep communications professional. Vulgar or abusive language will not be tolerated and may result in request removal.
        </p>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Urgency Level
        </label>
        <div className="flex gap-4">
          {(['Low', 'Medium', 'High'] as const).map(level => (
            <label key={level} className="flex items-center">
              <input
                type="radio"
                value={level}
                checked={form.urgency === level}
                onChange={(e) => onFormChange({ urgency: e.target.value as 'Low' | 'Medium' | 'High' })}
                className="mr-2"
              />
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                level === 'Low' ? 'bg-green-100 text-green-800' :
                level === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {level}
              </span>
            </label>
          ))}
        </div>
      </div>

      <div className="mb-8">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          How would you like us to contact you?
        </label>
        <div className="space-y-3">
          {[
            { value: 'call' as const, icon: Phone, label: 'Call my room or mobile', detail: form.phone },
            { value: 'text' as const, icon: MessageSquare, label: 'Text message', detail: form.phone },
            { value: 'email' as const, icon: Mail, label: 'Email notification', detail: form.email }
          ].map(option => (
            <label key={option.value} className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
              <input
                type="radio"
                value={option.value}
                checked={form.contact === option.value}
                onChange={(e) => onFormChange({ contact: e.target.value as 'call' | 'text' | 'email' })}
                className="text-blue-600"
              />
              <option.icon className="w-5 h-5 text-gray-600" />
              <div className="flex-1">
                <div className="font-medium">{option.label}</div>
                <div className="text-sm text-gray-500">{option.detail}</div>
              </div>
            </label>
          ))}
        </div>
      </div>

      <button
        type="submit"
        className={`w-full text-white px-6 py-3 rounded-lg font-semibold transition-colors ${service.buttonColor}`}
      >
        Submit Request
      </button>
    </form>
  );
};

export default ServiceRequestForm;