# Development Guide

## Overview

This guide provides comprehensive information for developers working on HospitalityFlows. It covers development setup, coding standards, testing procedures, and contribution workflows.

## Table of Contents

- [Development Setup](#development-setup)
- [Project Structure](#project-structure)
- [Coding Standards](#coding-standards)
- [Testing](#testing)
- [Git Workflow](#git-workflow)
- [Code Review Process](#code-review-process)
- [Debugging](#debugging)
- [Performance Guidelines](#performance-guidelines)
- [Security Considerations](#security-considerations)

## Development Setup

### Prerequisites

- **Node.js** 18+ (LTS recommended)
- **npm** 9+ or **yarn** 1.22+
- **Git** 2.30+
- **VS Code** (recommended) with extensions:
  - TypeScript and JavaScript Language Features
  - ES7+ React/Redux/React-Native snippets
  - Tailwind CSS IntelliSense
  - ESLint
  - Prettier

### Initial Setup

```bash
# Clone the repository
git clone https://github.com/KrunchMuffin/hospitalityflows-demo.git
cd hospitalityflows-demo

# Install dependencies
npm install

# Copy environment variables
cp .env.example .env

# Start development server
npm run dev
```

### Environment Configuration

Create a `.env` file with the following variables:

```bash
# Required - Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Optional - Development
VITE_N8N_WEBHOOK_URL=http://localhost:5678/webhook/guest-services
VITE_DEBUG=true
```

### Development Scripts

```bash
# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run linting
npm run lint

# Fix linting issues
npm run lint -- --fix

# Type checking
npx tsc --noEmit
```

## Project Structure

### Directory Organization

```
src/
├── components/          # Reusable React components
│   ├── admin/          # Admin panel components
│   ├── guest/          # Guest-facing components
│   ├── staff/          # Staff dashboard components
│   └── *.tsx           # Shared components
├── contexts/           # React contexts
├── lib/               # Utility libraries and configurations
├── pages/             # Main application pages
├── App.tsx            # Root application component
├── main.tsx           # Application entry point
└── index.css          # Global styles
```

### File Naming Conventions

- **Components:** PascalCase (`ServiceCard.tsx`)
- **Pages:** PascalCase (`GuestServicesPage.tsx`)
- **Utilities:** camelCase (`tenant-config.ts`)
- **Types:** PascalCase interfaces (`ServiceConfig`)
- **Constants:** UPPER_SNAKE_CASE (`DEFAULT_TENANT_ID`)

### Import Organization

```typescript
// 1. React and external libraries
import React, { useState, useEffect } from 'react';
import { Link, useParams } from 'react-router-dom';
import { Wrench, Sparkles } from 'lucide-react';

// 2. Internal utilities and contexts
import { useToast } from '../contexts/ToastContext';
import { supabase } from '../lib/supabase';
import { getCurrentTenant } from '../lib/tenant-config';

// 3. Components
import ServiceCard from '../components/guest/ServiceCard';
import AnnouncementBar from '../components/guest/AnnouncementBar';
```

## Coding Standards

### TypeScript Guidelines

#### Interface Definitions

```typescript
// Use descriptive interface names
interface ServiceRequestForm {
  service_type: string;
  description: string;
  urgency: 'low' | 'medium' | 'high';
  guest_name?: string;
  guest_contact?: string;
}

// Export interfaces for reuse
export interface ComponentProps {
  required: string;
  optional?: number;
  callback: (value: string) => void;
}
```

#### Type Safety

```typescript
// Use strict typing
const handleSubmit = (data: ServiceRequestForm): Promise<void> => {
  // Implementation
};

// Avoid 'any' type
// Bad
const processData = (data: any) => { ... };

// Good
const processData = (data: unknown) => {
  if (isValidData(data)) {
    // Type-safe processing
  }
};
```

### React Component Standards

#### Functional Components

```typescript
// Use React.FC for component typing
const ServiceCard: React.FC<ServiceCardProps> = ({
  serviceKey,
  service,
  onClick
}) => {
  // Component logic
  return (
    <div className="service-card">
      {/* JSX */}
    </div>
  );
};

export default ServiceCard;
```

#### Hooks Usage

```typescript
// Custom hooks for reusable logic
const useServiceRequest = (roomNumber: string) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const submitRequest = async (data: ServiceRequestForm) => {
    setLoading(true);
    try {
      // Submit logic
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };
  
  return { loading, error, submitRequest };
};
```

#### State Management

```typescript
// Use appropriate state structure
const [formData, setFormData] = useState<ServiceRequestForm>({
  service_type: '',
  description: '',
  urgency: 'medium',
  guest_name: '',
  guest_contact: ''
});

// Update state immutably
const updateFormField = (field: keyof ServiceRequestForm, value: string) => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};
```

### CSS and Styling

#### Tailwind CSS Usage

```typescript
// Use consistent class patterns
const buttonClasses = "bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md transition-colors";

// Responsive design
const gridClasses = "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";

// Conditional classes
const cardClasses = `
  bg-white rounded-lg shadow-lg p-6
  ${isActive ? 'ring-2 ring-blue-500' : ''}
  ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-xl'}
`;
```

#### CSS Variables for Theming

```css
/* Use CSS variables for tenant theming */
:root {
  --tenant-primary-color: #1e40af;
  --tenant-secondary-color: #3b82f6;
}

.tenant-themed {
  background-color: var(--tenant-primary-color);
}
```

### Error Handling

#### Component Error Boundaries

```typescript
class ErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Component error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <div>Something went wrong.</div>;
    }

    return this.props.children;
  }
}
```

#### Async Error Handling

```typescript
const fetchData = async () => {
  try {
    const { data, error } = await supabase
      .from('table_name')
      .select('*');
      
    if (error) throw error;
    
    return data;
  } catch (error) {
    console.error('Database error:', error);
    showToast('Failed to load data', 'error');
    throw error; // Re-throw for component handling
  }
};
```

## Testing

### Testing Strategy

#### Unit Tests

```typescript
// Component testing with React Testing Library
import { render, screen, fireEvent } from '@testing-library/react';
import { ToastProvider } from '../contexts/ToastContext';
import ServiceCard from './ServiceCard';

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ToastProvider>
      {component}
    </ToastProvider>
  );
};

describe('ServiceCard', () => {
  const mockService = {
    title: 'Maintenance',
    icon: Wrench,
    color: 'bg-red-100',
    buttonColor: 'bg-red-600',
    options: ['AC Issue', 'Plumbing']
  };

  test('renders service information correctly', () => {
    renderWithProviders(
      <ServiceCard
        serviceKey="maintenance"
        service={mockService}
        onClick={jest.fn()}
      />
    );

    expect(screen.getByText('Maintenance')).toBeInTheDocument();
  });

  test('calls onClick when clicked', () => {
    const mockClick = jest.fn();
    
    renderWithProviders(
      <ServiceCard
        serviceKey="maintenance"
        service={mockService}
        onClick={mockClick}
      />
    );

    fireEvent.click(screen.getByRole('button'));
    expect(mockClick).toHaveBeenCalledTimes(1);
  });
});
```

#### Integration Tests

```typescript
// Test component integration with Supabase
import { createClient } from '@supabase/supabase-js';

// Mock Supabase for testing
jest.mock('../lib/supabase', () => ({
  supabase: {
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          data: mockData,
          error: null
        }))
      }))
    }))
  }
}));

test('loads announcements from database', async () => {
  const mockData = [
    { id: '1', headline: 'Test', content: 'Test content' }
  ];

  renderWithProviders(<AnnouncementBar announcements={[]} />);
  
  // Test async data loading
  await waitFor(() => {
    expect(screen.getByText('Test')).toBeInTheDocument();
  });
});
```

### Test Configuration

#### Jest Setup

```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/setupTests.ts'],
  moduleNameMapping: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  transform: {
    '^.+\\.(ts|tsx)$': 'ts-jest',
  },
};
```

#### Testing Utilities

```typescript
// src/test-utils.tsx
import React from 'react';
import { render } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ToastProvider } from './contexts/ToastContext';

export const renderWithProviders = (
  ui: React.ReactElement,
  options = {}
) => {
  const Wrapper = ({ children }: { children: React.ReactNode }) => (
    <BrowserRouter>
      <ToastProvider>
        {children}
      </ToastProvider>
    </BrowserRouter>
  );

  return render(ui, { wrapper: Wrapper, ...options });
};
```

## Git Workflow

### Branch Strategy

```bash
# Main branches
main          # Production-ready code
develop       # Integration branch for features

# Feature branches
feature/      # New features
bugfix/       # Bug fixes
hotfix/       # Critical production fixes
```

### Commit Message Format

Use conventional commits:

```bash
# Format: type(scope): description

feat(auth): add user authentication
fix(ui): resolve mobile navigation issue
docs(api): update database schema documentation
style(components): improve button styling
refactor(utils): optimize tenant configuration
test(services): add unit tests for service requests
chore(deps): update dependencies
```

### Branch Workflow

```bash
# Create feature branch
git checkout -b feature/new-service-type

# Make changes and commit
git add .
git commit -m "feat(services): add spa service type"

# Push branch
git push origin feature/new-service-type

# Create pull request
# After review and approval, merge to develop
```

### Pre-commit Hooks

```bash
# Install husky for git hooks
npm install --save-dev husky

# Set up pre-commit hook
npx husky add .husky/pre-commit "npm run lint"
npx husky add .husky/pre-commit "npm run test"
```

## Code Review Process

### Review Checklist

#### Functionality
- [ ] Code works as intended
- [ ] Edge cases are handled
- [ ] Error handling is appropriate
- [ ] Performance is acceptable

#### Code Quality
- [ ] Code follows project standards
- [ ] TypeScript types are correct
- [ ] Components are properly structured
- [ ] No console.log statements in production code

#### Testing
- [ ] Tests are included for new features
- [ ] Existing tests still pass
- [ ] Test coverage is adequate

#### Documentation
- [ ] Code is self-documenting
- [ ] Complex logic is commented
- [ ] README is updated if needed

### Review Guidelines

#### For Authors
- Keep pull requests small and focused
- Write clear commit messages
- Include tests for new functionality
- Update documentation as needed

#### For Reviewers
- Be constructive and specific
- Focus on code quality and maintainability
- Test the changes locally if needed
- Approve only when confident in the changes

## Debugging

### Development Tools

#### Browser DevTools
- React Developer Tools
- Redux DevTools (if using Redux)
- Network tab for API calls
- Console for error messages

#### VS Code Debugging

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch Chrome",
      "request": "launch",
      "type": "chrome",
      "url": "http://localhost:5173",
      "webRoot": "${workspaceFolder}/src"
    }
  ]
}
```

### Common Debugging Scenarios

#### Supabase Connection Issues

```typescript
// Debug Supabase queries
const debugQuery = async () => {
  console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
  
  const { data, error } = await supabase
    .from('table_name')
    .select('*');
    
  console.log('Query result:', { data, error });
};
```

#### Tenant Configuration Issues

```typescript
// Debug tenant detection
const debugTenant = () => {
  console.log('Current hostname:', window.location.hostname);
  console.log('URL params:', new URLSearchParams(window.location.search));
  console.log('Current tenant:', getCurrentTenant());
};
```

#### Component State Issues

```typescript
// Debug component state
const MyComponent = () => {
  const [state, setState] = useState(initialState);
  
  useEffect(() => {
    console.log('State changed:', state);
  }, [state]);
  
  // Component logic
};
```

## Performance Guidelines

### React Performance

#### Memoization

```typescript
// Memoize expensive components
const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* Expensive rendering */}</div>;
});

// Memoize expensive calculations
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

// Memoize callbacks
const handleClick = useCallback(() => {
  // Handle click
}, [dependency]);
```

#### Code Splitting

```typescript
// Lazy load components
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// Usage with Suspense
<Suspense fallback={<div>Loading...</div>}>
  <LazyComponent />
</Suspense>
```

### Bundle Optimization

#### Analyze Bundle Size

```bash
# Build and analyze
npm run build
npx vite-bundle-analyzer dist
```

#### Tree Shaking

```typescript
// Import only what you need
import { useState, useEffect } from 'react';
import { Wrench } from 'lucide-react';

// Avoid importing entire libraries
// Bad: import * as icons from 'lucide-react';
// Good: import { Wrench, Settings } from 'lucide-react';
```

## Security Considerations

### Environment Variables

```typescript
// Never expose sensitive data
// Bad
const API_KEY = 'secret-key-123';

// Good
const API_KEY = import.meta.env.VITE_API_KEY;
```

### Input Validation

```typescript
// Validate and sanitize inputs
const validateRoomNumber = (roomNumber: string): boolean => {
  return /^[A-Z0-9-]{1,20}$/i.test(roomNumber);
};

const sanitizeInput = (input: string): string => {
  return input.trim().replace(/[<>]/g, '');
};
```

### XSS Prevention

```typescript
// Use React's built-in XSS protection
// Safe - React escapes by default
<div>{userInput}</div>

// Dangerous - avoid dangerouslySetInnerHTML
<div dangerouslySetInnerHTML={{ __html: userInput }} />
```

### CSRF Protection

```typescript
// Use proper authentication headers
const makeAuthenticatedRequest = async (data: any) => {
  const response = await fetch('/api/endpoint', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });
  
  return response.json();
};
```

## Additional Resources

### Documentation
- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Vite Documentation](https://vitejs.dev/guide/)
- [Supabase Documentation](https://supabase.com/docs)

### Tools
- [React Developer Tools](https://chrome.google.com/webstore/detail/react-developer-tools/)
- [TypeScript Playground](https://www.typescriptlang.org/play)
- [Tailwind CSS Playground](https://play.tailwindcss.com/)

### Community
- [React Community](https://reactjs.org/community/support.html)
- [TypeScript Community](https://www.typescriptlang.org/community/)
- [Supabase Community](https://supabase.com/docs/guides/getting-started)

---

For questions about development practices or to suggest improvements to this guide, please contact the development team or open an issue in the repository.
