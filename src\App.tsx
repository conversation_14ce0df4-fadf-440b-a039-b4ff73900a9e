import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';

// Page components
import HomePage from './pages/HomePage';
import GuestServicesPage from './pages/GuestServicesPage';
import StaffDashboard from './pages/StaffDashboard';
import AdminPanel from './pages/AdminPanel';
import DemoShowcase from './pages/DemoShowcase';

// Utility components
import TenantDebugger from './components/TenantDebugger';

// Context providers
import { ToastProvider } from './contexts/ToastContext';

// Tenant configuration utilities
import { getCurrentTenant } from './lib/tenant-config';

/**
 * Main Application Component
 *
 * This is the root component that sets up:
 * - Multi-tenant theming based on current tenant configuration
 * - Application routing for all pages
 * - Global context providers (Toast notifications)
 * - Development debugging tools
 */
function App() {
  /**
   * Initialize tenant-specific theming and page title
   * This effect runs once on app load to:
   * 1. Apply tenant-specific CSS custom properties for theming
   * 2. Set the browser tab title to include the hotel name
   */
  useEffect(() => {
    // Get the current tenant configuration based on domain/subdomain
    const tenant = getCurrentTenant();

    // Apply tenant-specific theme colors as CSS custom properties
    // These can be used throughout the app for consistent branding
    if (tenant.branding?.primaryColor) {
      document.documentElement.style.setProperty('--tenant-primary-color', tenant.branding.primaryColor);
    }
    if (tenant.branding?.secondaryColor) {
      document.documentElement.style.setProperty('--tenant-secondary-color', tenant.branding.secondaryColor);
    }

    // Update the browser tab title to include the hotel name
    document.title = `${tenant.name} - Guest Services`;
  }, []); // Empty dependency array - only run once on mount

  return (
    // Wrap the entire app with the Toast notification provider
    <ToastProvider>
      {/* Set up React Router for client-side routing */}
      <Router>
        {/* Main app container with full height and light background */}
        <div className="min-h-screen bg-gray-50">
          {/* Define all application routes */}
          <Routes>
            {/* Hotel homepage - shows branding, announcements, and featured room */}
            <Route path="/" element={<HomePage />} />

            {/* Guest services portal - dynamic room number from URL parameter */}
            <Route path="/room/:roomNumber" element={<GuestServicesPage />} />

            {/* Staff dashboard - for monitoring requests and room status */}
            <Route path="/staff" element={<StaffDashboard />} />

            {/* Admin panel - for managing announcements and generating QR codes */}
            <Route path="/admin" element={<AdminPanel />} />

            {/* Demo showcase - interactive demo of platform features */}
            <Route path="/demo" element={<DemoShowcase />} />
          </Routes>

          {/* Development-only tenant debugging component (hidden in production) */}
          <TenantDebugger />
        </div>
      </Router>
    </ToastProvider>
  );
}

export default App;
