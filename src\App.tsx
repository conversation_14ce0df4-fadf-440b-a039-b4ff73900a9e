import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import HomePage from './pages/HomePage';
import GuestServicesPage from './pages/GuestServicesPage';
import StaffDashboard from './pages/StaffDashboard';
import AdminPanel from './pages/AdminPanel';
import DemoShowcase from './pages/DemoShowcase';
import TenantDebugger from './components/TenantDebugger';
import { ToastProvider } from './contexts/ToastContext';
import { getCurrentTenant } from './lib/tenant-config';

function App() {
  useEffect(() => {
    // Apply tenant-specific theme colors
    const tenant = getCurrentTenant();
    if (tenant.branding?.primaryColor) {
      document.documentElement.style.setProperty('--tenant-primary-color', tenant.branding.primaryColor);
    }
    if (tenant.branding?.secondaryColor) {
      document.documentElement.style.setProperty('--tenant-secondary-color', tenant.branding.secondaryColor);
    }
    
    // Update page title
    document.title = `${tenant.name} - Guest Services`;
  }, []);

  return (
    <ToastProvider>
      <Router>
        <div className="min-h-screen bg-gray-50">
          <Routes>
            <Route path="/" element={<HomePage />} />
            <Route path="/room/:roomNumber" element={<GuestServicesPage />} />
            <Route path="/staff" element={<StaffDashboard />} />
            <Route path="/admin" element={<AdminPanel />} />
            <Route path="/demo" element={<DemoShowcase />} />
          </Routes>
          <TenantDebugger />
        </div>
      </Router>
    </ToastProvider>
  );
}

export default App;