# Next Steps for Production Deployment

## 1. Install QR Code Library
```bash
npm install qrcode.react @types/qrcode.react
```

## 2. Update RoomQRCode Component
Replace the placeholder QR code with actual QR generation using the installed library.

## 3. Room Management Features
Add admin features to:
- Generate QR codes for any room number
- Print QR codes with room numbers
- Bulk generate QR codes for all rooms

## 4. Database Schema Updates
Ensure all tables have proper tenant isolation:
```sql
-- Add to all tables if not present
ALTER TABLE guest_requests ADD COLUMN IF NOT EXISTS room_number VARCHAR(50);
ALTER TABLE work_orders ADD COLUMN IF NOT EXISTS room_number VARCHAR(50);
```

## 5. n8n Workflow Updates
Update workflows to:
- Handle dynamic room numbers
- Route based on tenant_id AND room_number
- Support various room number formats

## 6. Testing Checklist
- [ ] Test with different room number formats (101, A-12, PH-1)
- [ ] Verify tenant isolation
- [ ] Test QR code scanning
- [ ] Confirm webhook payloads include room numbers
- [ ] Test staff dashboard with real room data

## 7. Production Deployment
1. Remove demo-specific content from non-demo tenants
2. Generate QR codes for each hotel's rooms
3. Update staff training materials
4. Monitor for any hardcoded references

## Room Number Best Practices
- Support alphanumeric room numbers
- Allow special characters (hyphens, dots)
- No length restrictions
- Case-insensitive matching
