import React from 'react';
import { Database, Phone, Mail, Edit3 } from 'lucide-react';

interface GuestInfoProps {
  phone: string;
  email: string;
  isEditingPhone: boolean;
  isEditingEmail: boolean;
  onPhoneChange: (value: string) => void;
  onEmailChange: (value: string) => void;
  onEditPhone: () => void;
  onEditEmail: () => void;
  onPhoneEditComplete: () => void;
  onEmailEditComplete: () => void;
}

const GuestInfoSection: React.FC<GuestInfoProps> = ({
  phone,
  email,
  isEditingPhone,
  isEditingEmail,
  onPhoneChange,
  onEmailChange,
  onEditPhone,
  onEditEmail,
  onPhoneEditComplete,
  onEmailEditComplete
}) => {
  return (
    <div className="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div className="flex items-center gap-2 mb-3">
        <Database className="w-5 h-5 text-blue-600" />
        <h3 className="text-lg font-semibold text-blue-900">Guest Information</h3>
        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Retrieved from PMS</span>
      </div>

      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Phone className="w-4 h-4 text-gray-600" />
            <div>
              <span className="text-sm text-gray-600">Phone:</span>
              {isEditingPhone ? (
                <input
                  type="tel"
                  value={phone}
                  onChange={(e) => onPhoneChange(e.target.value)}
                  className="ml-2 px-2 py-1 border border-gray-300 rounded text-sm"
                  onBlur={onPhoneEditComplete}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      onPhoneEditComplete();
                    }
                  }}
                  autoFocus
                />
              ) : (
                <span className="ml-2 font-medium text-gray-900">{phone}</span>
              )}
            </div>
          </div>
          {!isEditingPhone && (
            <button
              type="button"
              onClick={onEditPhone}
              className="text-blue-600 hover:text-blue-800 p-1"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Mail className="w-4 h-4 text-gray-600" />
            <div>
              <span className="text-sm text-gray-600">Email:</span>
              {isEditingEmail ? (
                <input
                  type="email"
                  value={email}
                  onChange={(e) => onEmailChange(e.target.value)}
                  className="ml-2 px-2 py-1 border border-gray-300 rounded text-sm"
                  onBlur={onEmailEditComplete}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      onEmailEditComplete();
                    }
                  }}
                  autoFocus
                />
              ) : (
                <span className="ml-2 font-medium text-gray-900">{email}</span>
              )}
            </div>
          </div>
          {!isEditingEmail && (
            <button
              type="button"
              onClick={onEditEmail}
              className="text-blue-600 hover:text-blue-800 p-1"
            >
              <Edit3 className="w-4 h-4" />
            </button>
          )}
        </div>
      </div>

      <p className="text-xs text-blue-600 mt-3">
        Contact information retrieved from Property Management System. Click edit icon to update.
      </p>
    </div>
  );
};

export default GuestInfoSection;