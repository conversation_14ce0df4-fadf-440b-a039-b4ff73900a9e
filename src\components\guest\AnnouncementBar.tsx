import React from 'react';
import { AlertCircle, Calendar, Gift, Utensils, ExternalLink } from 'lucide-react';
import { GuestAnnouncement } from '../../lib/supabase';

interface AnnouncementBarProps {
  announcements: GuestAnnouncement[];
  maxDisplay?: number;
}

const AnnouncementBar: React.FC<AnnouncementBarProps> = ({ announcements, maxDisplay = 3 }) => {
  const getAnnouncementIcon = (type: string | null) => {
    switch (type) {
      case 'dining': return <Utensils className="w-5 h-5" />;
      case 'event': return <Calendar className="w-5 h-5" />;
      case 'promotion': return <Gift className="w-5 h-5" />;
      case 'alert': return <AlertCircle className="w-5 h-5" />;
      default: return <AlertCircle className="w-5 h-5" />;
    }
  };

  if (announcements.length === 0) return null;

  return (
    <div className="bg-gray-900 text-white">
      {announcements.slice(0, maxDisplay).map((announcement) => (
        <div
          key={announcement.id}
          className="border-b border-gray-800 last:border-b-0"
          style={{
            backgroundColor: announcement.background_color,
            color: announcement.text_color
          }}
        >
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getAnnouncementIcon(announcement.announcement_type)}
                <div>
                  <span className="font-semibold">{announcement.headline}</span>
                  <span className="mx-2">•</span>
                  <span className="opacity-90">{announcement.content}</span>
                </div>
              </div>
              {announcement.action_text && announcement.action_url && (
                <a
                  href={announcement.action_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-1 px-3 py-1 bg-white/20 hover:bg-white/30 rounded-md text-sm font-medium transition-colors"
                >
                  {announcement.action_text}
                  <ExternalLink className="w-3 h-3" />
                </a>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default AnnouncementBar;