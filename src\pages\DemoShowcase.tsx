import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Clock, Zap, Users, CheckCircle } from 'lucide-react';
import { getCurrentTenant } from '../lib/tenant-config';

const DemoShowcase: React.FC = () => {
  const tenant = getCurrentTenant();
  const featuredRoom = tenant.settings?.demoConfig?.featuredRoom || '101';
  const [isRunningDemo, setIsRunningDemo] = useState(false);
  const [demoStep, setDemoStep] = useState(0);
  const [timer, setTimer] = useState(0);

  const demoSteps = [
    { step: 'Guest submits request', time: 1 },
    { step: 'System processes request', time: 2 },
    { step: 'Staff notification sent', time: 3 },
    { step: 'Request assigned automatically', time: 5 },
    { step: 'Work order created', time: 7 },
    { step: 'Status updated in real-time', time: 10 }
  ];

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isRunningDemo) {
      interval = setInterval(() => {
        setTimer(prev => {
          const newTime = prev + 1;
          
          // Check if we should advance to next step
          const currentStepData = demoSteps[demoStep];
          if (currentStepData && newTime >= currentStepData.time) {
            if (demoStep < demoSteps.length - 1) {
              setDemoStep(prev => prev + 1);
            } else {
              // Demo complete
              setIsRunningDemo(false);
              setTimeout(() => {
                setTimer(0);
                setDemoStep(0);
              }, 2000);
            }
          }
          
          return newTime;
        });
      }, 1000);
    }
    
    return () => clearInterval(interval);
  }, [isRunningDemo, demoStep]);

  const startDemo = () => {
    setIsRunningDemo(true);
    setTimer(0);
    setDemoStep(0);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-blue-900 text-white py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Link to="/" className="flex items-center gap-2 text-amber-400 hover:text-amber-300 mb-4 transition-colors">
            <ArrowLeft className="w-4 h-4" />
            Back to Hotel
          </Link>
          <h1 className="text-3xl font-bold">HospitalityFlows Demo</h1>
          <p className="text-blue-200 mt-2">See the difference automation makes</p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Comparison Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {/* Manual Process */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="bg-red-50 border-b border-red-200 p-6">
              <h2 className="text-2xl font-bold text-red-800 flex items-center gap-3">
                <Clock className="w-8 h-8" />
                Manual Process
              </h2>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-red-600 mb-2">2-4 Hours</div>
                <div className="text-gray-600">Average Response Time</div>
              </div>
              
              <div className="space-y-4">
                {[
                  'Guest calls front desk',
                  'Front desk takes notes',
                  'Staff member contacted by phone',
                  'Paper work order created',
                  'Manual assignment process',
                  'Physical status updates',
                  'Guest follow-up required'
                ].map((step, index) => (
                  <div key={index} className="flex items-center gap-3 text-gray-700">
                    <div className="w-6 h-6 bg-red-100 text-red-600 rounded-full flex items-center justify-center text-sm font-bold">
                      {index + 1}
                    </div>
                    <span>{step}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-red-50 rounded-lg">
                <p className="text-red-800 font-medium">Challenges:</p>
                <ul className="text-red-700 text-sm mt-2 space-y-1">
                  <li>• Multiple handoffs and delays</li>
                  <li>• Manual tracking and errors</li>
                  <li>• Poor guest communication</li>
                  <li>• Inefficient resource allocation</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Automated Process */}
          <div className="bg-white rounded-lg shadow-lg overflow-hidden">
            <div className="bg-green-50 border-b border-green-200 p-6">
              <h2 className="text-2xl font-bold text-green-800 flex items-center gap-3">
                <Zap className="w-8 h-8" />
                HospitalityFlows Automation
              </h2>
            </div>
            <div className="p-6">
              <div className="text-center mb-6">
                <div className="text-4xl font-bold text-green-600 mb-2">30 Seconds</div>
                <div className="text-gray-600">Average Response Time</div>
              </div>
              
              <div className="space-y-4">
                {demoSteps.map((stepData, index) => (
                  <div 
                    key={index} 
                    className={`flex items-center gap-3 transition-all duration-500 ${
                      isRunningDemo && index <= demoStep 
                        ? 'text-green-700 font-medium' 
                        : 'text-gray-500'
                    }`}
                  >
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold transition-all duration-500 ${
                      isRunningDemo && index <= demoStep
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-500'
                    }`}>
                      {isRunningDemo && index <= demoStep ? (
                        <CheckCircle className="w-4 h-4" />
                      ) : (
                        index + 1
                      )}
                    </div>
                    <span>{stepData.step}</span>
                  </div>
                ))}
              </div>
              
              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <p className="text-green-800 font-medium">Benefits:</p>
                <ul className="text-green-700 text-sm mt-2 space-y-1">
                  <li>• Instant request processing</li>
                  <li>• Automated staff assignment</li>
                  <li>• Real-time status updates</li>
                  <li>• Enhanced guest satisfaction</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Live Demo Section */}
        <div className="bg-white rounded-lg shadow-lg p-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">Live Demo</h2>
          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Watch our automation system process a guest request in real-time. 
            See how quickly requests are handled and staff are notified.
          </p>
          
          <div className="mb-8">
            {isRunningDemo ? (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 inline-block">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {timer}s
                </div>
                <div className="text-blue-800">Processing request...</div>
                {demoStep < demoSteps.length && (
                  <div className="text-sm text-blue-600 mt-2">
                    {demoSteps[demoStep].step}
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={startDemo}
                className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold text-lg transition-colors"
              >
                Start Live Demo
              </button>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="text-center">
              <Users className="w-12 h-12 text-blue-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Guest Experience</h3>
              <p className="text-gray-600 text-sm">Simple, intuitive interface for all service requests</p>
            </div>
            <div className="text-center">
              <Zap className="w-12 h-12 text-amber-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Instant Processing</h3>
              <p className="text-gray-600 text-sm">Automated workflows eliminate manual delays</p>
            </div>
            <div className="text-center">
              <CheckCircle className="w-12 h-12 text-green-600 mx-auto mb-3" />
              <h3 className="font-semibold text-gray-900 mb-2">Staff Efficiency</h3>
              <p className="text-gray-600 text-sm">Real-time updates and smart task management</p>
            </div>
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-blue-900 text-white rounded-lg p-8 text-center mt-12">
          <h2 className="text-3xl font-bold mb-4">Ready to Transform Your Hotel?</h2>
          <p className="text-blue-200 mb-8 max-w-2xl mx-auto">
            Join hundreds of hotels already using HospitalityFlows to deliver exceptional guest experiences 
            while improving operational efficiency.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to={`/room/${featuredRoom}`}
              className="bg-amber-600 hover:bg-amber-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              Try Guest Services
            </Link>
            <Link
              to="/staff"
              className="bg-transparent border-2 border-white hover:bg-white hover:text-blue-900 text-white px-8 py-3 rounded-lg font-semibold transition-colors"
            >
              View Staff Dashboard
            </Link>
          </div>
          
          <div className="mt-8 pt-8 border-t border-blue-800">
            <p className="text-blue-200">
              Powered by <span className="text-amber-400 font-semibold">HospitalityFlows.com</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DemoShowcase;