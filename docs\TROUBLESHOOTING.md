# Troubleshooting Guide

## Overview

This guide provides solutions to common issues encountered when developing, deploying, or using HospitalityFlows. Issues are organized by category with step-by-step solutions.

## Table of Contents

- [Development Issues](#development-issues)
- [Environment Variables](#environment-variables)
- [Database and Supabase Issues](#database-and-supabase-issues)
- [Multi-Tenant Issues](#multi-tenant-issues)
- [Build and Deployment Issues](#build-and-deployment-issues)
- [QR Code Issues](#qr-code-issues)
- [Performance Issues](#performance-issues)
- [Browser Compatibility](#browser-compatibility)
- [Frequently Asked Questions](#frequently-asked-questions)

## Development Issues

### Development Server Won't Start

**Problem:** `npm run dev` fails or shows errors

**Solutions:**

1. **Check Node.js version**
```bash
node --version  # Should be 18+
npm --version   # Should be 9+
```

2. **Clear cache and reinstall dependencies**
```bash
rm -rf node_modules package-lock.json
npm install
```

3. **Check for port conflicts**
```bash
# If port 5173 is in use
npx kill-port 5173
# Or specify a different port
npm run dev -- --port 3000
```

4. **Check for TypeScript errors**
```bash
npx tsc --noEmit
```

### Hot Reload Not Working

**Problem:** Changes don't reflect automatically in the browser

**Solutions:**

1. **Check file watching limits (Linux/macOS)**
```bash
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

2. **Disable browser cache**
   - Open DevTools (F12)
   - Go to Network tab
   - Check "Disable cache"

3. **Check Vite configuration**
```typescript
// vite.config.ts
export default defineConfig({
  server: {
    watch: {
      usePolling: true, // Enable if file watching doesn't work
    },
  },
});
```

### Import/Module Errors

**Problem:** Module not found or import errors

**Solutions:**

1. **Check file paths and extensions**
```typescript
// Correct
import { getCurrentTenant } from '../lib/tenant-config';

// Incorrect (missing extension in some setups)
import { getCurrentTenant } from '../lib/tenant-config.ts';
```

2. **Check TypeScript configuration**
```json
// tsconfig.json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["src/*"]
    }
  }
}
```

3. **Restart TypeScript server in VS Code**
   - Ctrl+Shift+P → "TypeScript: Restart TS Server"

## Environment Variables

### Variables Not Loading

**Problem:** Environment variables are undefined in the application

**Solutions:**

1. **Check variable naming**
```bash
# Correct - must start with VITE_
VITE_SUPABASE_URL=https://your-project.supabase.co

# Incorrect - won't be loaded by Vite
SUPABASE_URL=https://your-project.supabase.co
```

2. **Check .env file location**
```
project-root/
├── .env          # ✓ Correct location
├── src/
│   └── .env      # ✗ Wrong location
└── package.json
```

3. **Restart development server**
```bash
# Stop server (Ctrl+C) and restart
npm run dev
```

4. **Check .env file syntax**
```bash
# Correct
VITE_SUPABASE_URL=https://example.supabase.co
VITE_SUPABASE_ANON_KEY=your-key-here

# Incorrect - no spaces around =
VITE_SUPABASE_URL = https://example.supabase.co
```

### Environment Variables in Production

**Problem:** Variables work in development but not in production

**Solutions:**

1. **Set variables in deployment platform**
   - **Vercel:** Project Settings → Environment Variables
   - **Netlify:** Site Settings → Environment Variables
   - **Cloudflare:** Settings → Environment Variables

2. **Check build logs for missing variables**
```bash
# Vercel
vercel logs

# Netlify
netlify logs
```

3. **Verify variable names match exactly**
   - Case-sensitive
   - Must include `VITE_` prefix
   - No extra spaces or characters

## Database and Supabase Issues

### Connection Errors

**Problem:** "Failed to connect to Supabase" or timeout errors

**Solutions:**

1. **Verify Supabase credentials**
```typescript
// Check in browser console
console.log('Supabase URL:', import.meta.env.VITE_SUPABASE_URL);
console.log('Supabase Key:', import.meta.env.VITE_SUPABASE_ANON_KEY?.substring(0, 20) + '...');
```

2. **Check Supabase project status**
   - Visit Supabase dashboard
   - Verify project is active
   - Check for maintenance notifications

3. **Test connection manually**
```typescript
import { supabase } from './lib/supabase';

const testConnection = async () => {
  try {
    const { data, error } = await supabase
      .from('tenants')
      .select('count')
      .limit(1);
    
    console.log('Connection test:', { data, error });
  } catch (err) {
    console.error('Connection failed:', err);
  }
};
```

### RLS Policy Errors

**Problem:** "new row violates row-level security policy" errors

**Solutions:**

1. **Check RLS policies in Supabase**
```sql
-- View existing policies
SELECT * FROM pg_policies WHERE tablename = 'your_table_name';
```

2. **Ensure tenant_id is set correctly**
```typescript
const { data, error } = await supabase
  .from('guest_announcements')
  .insert({
    tenant_id: getCurrentTenant().id, // Make sure this is set
    headline: 'Test',
    content: 'Test content'
  });
```

3. **Temporarily disable RLS for testing**
```sql
-- CAUTION: Only for debugging
ALTER TABLE your_table_name DISABLE ROW LEVEL SECURITY;
-- Remember to re-enable: ALTER TABLE your_table_name ENABLE ROW LEVEL SECURITY;
```

### Migration Issues

**Problem:** Database schema doesn't match application expectations

**Solutions:**

1. **Run pending migrations**
```bash
# If using Supabase CLI
supabase db push

# Or apply migrations manually in Supabase SQL editor
```

2. **Check table structure**
```sql
-- Verify table exists and has correct columns
\d guest_announcements
```

3. **Regenerate TypeScript types**
```bash
# If using Supabase CLI
supabase gen types typescript --project-id your-project-id > src/lib/database.types.ts
```

## Multi-Tenant Issues

### Tenant Not Found

**Problem:** Application shows "No tenant found" or defaults to demo tenant

**Solutions:**

1. **Check hostname/URL**
```javascript
// Debug tenant detection
console.log('Current hostname:', window.location.hostname);
console.log('URL params:', new URLSearchParams(window.location.search));
console.log('Detected tenant:', getCurrentTenant());
```

2. **Verify tenant configuration**
```typescript
// Check if tenant exists in config
import { tenants } from './lib/tenant-config';
console.log('Available tenants:', Object.keys(tenants));
```

3. **Clear tenant cache**
```typescript
import { clearTenantCache } from './lib/tenant-config';
clearTenantCache();
```

4. **Check DNS configuration**
```bash
# Verify subdomain points to correct domain
nslookup parkhotel.hospitalityflows.com
```

### Tenant Theming Not Applied

**Problem:** Tenant-specific colors or branding not showing

**Solutions:**

1. **Check CSS custom properties**
```javascript
// In browser console
console.log(getComputedStyle(document.documentElement).getPropertyValue('--tenant-primary-color'));
```

2. **Verify tenant branding configuration**
```typescript
const tenant = getCurrentTenant();
console.log('Tenant branding:', tenant.branding);
```

3. **Force theme refresh**
```typescript
// Manually trigger theme update
const tenant = getCurrentTenant();
if (tenant.branding?.primaryColor) {
  document.documentElement.style.setProperty('--tenant-primary-color', tenant.branding.primaryColor);
}
```

## Build and Deployment Issues

### Build Failures

**Problem:** `npm run build` fails with errors

**Solutions:**

1. **Check TypeScript errors**
```bash
npx tsc --noEmit
```

2. **Check for unused imports**
```bash
npm run lint
```

3. **Clear build cache**
```bash
rm -rf dist .vite
npm run build
```

4. **Check for circular dependencies**
```bash
npx madge --circular src/
```

### 404 Errors on Refresh

**Problem:** Page refreshes result in 404 errors

**Solutions:**

1. **Configure SPA redirects**

**Vercel:**
```json
// vercel.json
{
  "rewrites": [
    { "source": "/(.*)", "destination": "/index.html" }
  ]
}
```

**Netlify:**
```
# public/_redirects
/*    /index.html   200
```

**Cloudflare Pages:**
```
# public/_redirects
/*    /index.html   200
```

2. **Check deployment configuration**
   - Ensure build output directory is set to `dist`
   - Verify build command is `npm run build`

### Environment Variables Missing in Production

**Problem:** App works locally but fails in production due to missing env vars

**Solutions:**

1. **Check deployment platform settings**
   - Verify all `VITE_` prefixed variables are set
   - Check for typos in variable names
   - Ensure values don't contain special characters that need escaping

2. **Test build locally**
```bash
npm run build
npm run preview
```

3. **Check build logs**
   - Look for warnings about missing environment variables
   - Verify build process completes successfully

## QR Code Issues

### QR Codes Not Generating

**Problem:** QR codes appear blank or show errors

**Solutions:**

1. **Check qrcode.react installation**
```bash
npm list qrcode.react
# If not installed:
npm install qrcode.react
```

2. **Verify URL format**
```typescript
// Check the generated URL
const url = `https://${tenantSubdomain}.hospitalityflows.com/room/${roomNumber}`;
console.log('QR Code URL:', url);
```

3. **Check for special characters**
```typescript
// Ensure room number is URL-safe
const sanitizedRoomNumber = encodeURIComponent(roomNumber);
```

### QR Codes Not Scanning

**Problem:** QR codes generate but don't scan properly

**Solutions:**

1. **Increase error correction level**
```typescript
<QRCodeSVG
  value={url}
  level="H" // Highest error correction
  marginSize={4} // Ensure adequate margin
/>
```

2. **Check contrast**
```typescript
<QRCodeSVG
  value={url}
  bgColor="#FFFFFF" // White background
  fgColor="#000000" // Black foreground
/>
```

3. **Verify URL accessibility**
   - Test the URL manually in a browser
   - Ensure HTTPS is working
   - Check for redirects

### QR Code Download Issues

**Problem:** Cannot download QR codes as images

**Solutions:**

1. **Check browser permissions**
   - Allow downloads in browser settings
   - Disable popup blockers for the site

2. **Use alternative download method**
```typescript
// Convert SVG to PNG for download
const downloadQRCode = (svgElement: SVGElement, filename: string) => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  const data = new XMLSerializer().serializeToString(svgElement);
  const img = new Image();
  
  img.onload = () => {
    canvas.width = img.width;
    canvas.height = img.height;
    ctx?.drawImage(img, 0, 0);
    
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        URL.revokeObjectURL(url);
      }
    });
  };
  
  img.src = 'data:image/svg+xml;base64,' + btoa(data);
};
```

## Performance Issues

### Slow Page Load Times

**Problem:** Application takes too long to load

**Solutions:**

1. **Analyze bundle size**
```bash
npm run build
npx vite-bundle-analyzer dist
```

2. **Implement code splitting**
```typescript
// Lazy load heavy components
const AdminPanel = React.lazy(() => import('./pages/AdminPanel'));

// Use with Suspense
<Suspense fallback={<div>Loading...</div>}>
  <AdminPanel />
</Suspense>
```

3. **Optimize images**
   - Use WebP format when possible
   - Implement lazy loading for images
   - Compress images before uploading

4. **Enable compression**
   - Most hosting platforms enable gzip automatically
   - Verify compression is working in Network tab

### Slow Database Queries

**Problem:** Supabase queries take too long

**Solutions:**

1. **Add database indexes**
```sql
-- Index for tenant-based queries
CREATE INDEX idx_guest_announcements_tenant_active 
ON guest_announcements(tenant_id, is_active);
```

2. **Optimize queries**
```typescript
// Select only needed columns
const { data } = await supabase
  .from('guest_announcements')
  .select('id, headline, content') // Don't use select('*')
  .eq('tenant_id', tenantId)
  .limit(10);
```

3. **Implement pagination**
```typescript
const { data } = await supabase
  .from('requests')
  .select('*')
  .eq('tenant_id', tenantId)
  .range(0, 9) // First 10 items
  .order('created_at', { ascending: false });
```

## Browser Compatibility

### Internet Explorer Issues

**Problem:** Application doesn't work in older browsers

**Solutions:**

1. **Check browser support**
   - HospitalityFlows requires modern browsers (Chrome 90+, Firefox 88+, Safari 14+)
   - IE is not supported

2. **Add polyfills if needed**
```bash
npm install core-js
```

```typescript
// main.tsx
import 'core-js/stable';
```

### Mobile Safari Issues

**Problem:** Issues specific to iOS Safari

**Solutions:**

1. **Check viewport meta tag**
```html
<meta name="viewport" content="width=device-width, initial-scale=1.0">
```

2. **Test touch interactions**
   - Ensure buttons have adequate touch targets (44px minimum)
   - Test scrolling behavior

3. **Check for iOS-specific CSS issues**
```css
/* Fix iOS Safari input zoom */
input[type="text"], input[type="email"], input[type="tel"] {
  font-size: 16px;
}
```

## Frequently Asked Questions

### General Questions

**Q: Can I use this for multiple hotels?**
A: Yes! The application is designed for multi-tenancy. Each hotel gets its own subdomain and isolated data.

**Q: How do I add a new hotel?**
A: Add the hotel configuration to `src/lib/tenant-config.ts`, configure DNS, and deploy. See the [Configuration Guide](./README.md#adding-a-new-hotel) for details.

**Q: Is the application mobile-friendly?**
A: Yes, the application is fully responsive and optimized for mobile devices.

**Q: Can I customize the branding?**
A: Yes, each tenant can have custom colors, logos, and messaging configured in the tenant settings.

### Technical Questions

**Q: What databases are supported?**
A: The application uses Supabase (PostgreSQL) as the backend. Other databases would require significant code changes.

**Q: Can I self-host this?**
A: Yes, you can deploy to any static hosting service. You'll need your own Supabase instance for the backend.

**Q: How do I backup the data?**
A: Supabase provides automated backups. For additional backups, you can export data using the Supabase dashboard or API.

**Q: Is there an API for integration?**
A: The application uses Supabase's REST API. You can integrate with the same endpoints or use the provided webhook functionality.

### Development Questions

**Q: How do I contribute to the project?**
A: See the [Development Guide](./DEVELOPMENT_GUIDE.md) for contribution guidelines and development setup.

**Q: Can I add new service types?**
A: Yes, you can add new service types by updating the tenant configuration and adding corresponding UI components.

**Q: How do I test different tenants locally?**
A: Use URL parameters: `http://localhost:5173?tenant=demo` or `http://localhost:5173?tenant=parkhotel`

**Q: How do I debug tenant detection issues?**
A: Enable the tenant debugger (visible in development) or check the browser console for tenant detection logs.

## Getting Additional Help

### Support Channels

1. **GitHub Issues**
   - Report bugs: [GitHub Issues](https://github.com/KrunchMuffin/hospitalityflows-demo/issues)
   - Feature requests: Use the feature request template

2. **Documentation**
   - [API Documentation](./API_DOCUMENTATION.md)
   - [Component Documentation](./COMPONENT_DOCUMENTATION.md)
   - [Deployment Guide](./DEPLOYMENT_GUIDE.md)

3. **Community Resources**
   - [React Documentation](https://reactjs.org/docs)
   - [Supabase Documentation](https://supabase.com/docs)
   - [Vite Documentation](https://vitejs.dev/guide/)

### Before Asking for Help

1. **Check this troubleshooting guide**
2. **Search existing GitHub issues**
3. **Check browser console for errors**
4. **Verify environment variables are set correctly**
5. **Test with a fresh installation**

### When Reporting Issues

Include the following information:

1. **Environment details**
   - Operating system
   - Node.js version
   - Browser version
   - Deployment platform

2. **Steps to reproduce**
   - Exact steps that cause the issue
   - Expected vs actual behavior

3. **Error messages**
   - Full error messages from console
   - Screenshots if applicable

4. **Configuration**
   - Relevant environment variables (redacted)
   - Tenant configuration (if applicable)

---

This troubleshooting guide is regularly updated based on common issues. If you encounter a problem not covered here, please consider contributing a solution to help other developers.
