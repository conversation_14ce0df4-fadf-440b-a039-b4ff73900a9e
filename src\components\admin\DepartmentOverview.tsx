import React from 'react';
import { Department } from './DeviceManagement';

interface DepartmentOverviewProps {
  departments: Department[];
  deviceCount: (departmentName: string) => number;
}

const DepartmentOverview: React.FC<DepartmentOverviewProps> = ({ departments, deviceCount }) => {
  return (
    <div className="bg-white rounded-lg shadow-lg mb-6">
      <div className="p-6 border-b border-gray-200">
        <h2 className="text-xl font-bold text-gray-900">Department Overview</h2>
      </div>
      <div className="p-6">
        <div className="space-y-4">
          {departments.map(dept => {
            const count = deviceCount(dept.name);
            return (
              <div key={dept.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full ${dept.color}`}></div>
                  <span className="font-medium">{dept.name}</span>
                </div>
                <span className="text-sm text-gray-600">
                  {count} device{count !== 1 ? 's' : ''}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default DepartmentOverview;