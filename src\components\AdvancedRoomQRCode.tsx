import React from 'react';
import { QRCodeSVG } from 'qrcode.react';

interface AdvancedRoomQRCodeProps {
  roomNumber: string;
  tenantSubdomain: string;
  hotelName: string;
  logoUrl?: string;
  size?: number;
  primaryColor?: string;
  style?: 'modern' | 'classic' | 'minimal';
}

const AdvancedRoomQRCode: React.FC<AdvancedRoomQRCodeProps> = ({ 
  roomNumber, 
  tenantSubdomain,
  hotelName,
  logoUrl,
  size = 256,
  primaryColor = '#1e40af',
  style = 'modern'
}) => {
  const url = `https://${tenantSubdomain}.hospitalityflows.com/room/${roomNumber}`;
  
  // Different style configurations
  const styles = {
    modern: {
      containerClass: 'bg-gradient-to-br from-gray-50 to-gray-100 p-6 rounded-2xl shadow-xl',
      headerClass: 'text-lg font-bold mb-3',
      footerClass: 'text-sm text-gray-600 mt-3',
      qrPadding: 'p-4 bg-white rounded-xl shadow-inner',
    },
    classic: {
      containerClass: 'bg-white border-4 border-double border-gray-800 p-6',
      headerClass: 'text-xl font-serif mb-4 text-center',
      footerClass: 'text-sm font-serif text-gray-700 mt-4 text-center',
      qrPadding: 'p-3 border-2 border-gray-300',
    },
    minimal: {
      containerClass: 'bg-white p-4',
      headerClass: 'text-md font-medium mb-2',
      footerClass: 'text-xs text-gray-500 mt-2',
      qrPadding: 'p-2',
    }
  };

  const currentStyle = styles[style];

  return (
    <div className={currentStyle.containerClass}>
      <h3 className={currentStyle.headerClass} style={{ color: primaryColor }}>
        {hotelName}
      </h3>
      
      <div className={currentStyle.qrPadding}>
        <QRCodeSVG
          value={url}
          size={size}
          level="H"
          marginSize={0}
          bgColor="#FFFFFF"
          fgColor={primaryColor}
          imageSettings={logoUrl ? {
            src: logoUrl,
            height: size * 0.2,
            width: size * 0.2,
            excavate: true,
          } : undefined}
          title={`QR Code for ${hotelName} Room ${roomNumber}`}
        />
      </div>

      <div className={currentStyle.footerClass}>
        <p className="font-semibold">Room {roomNumber}</p>
        <p>Scan for Guest Services</p>
      </div>

      {style === 'modern' && (
        <div className="mt-4 text-xs text-gray-400 text-center">
          Instant service • 24/7 support
        </div>
      )}
    </div>
  );
};

export default AdvancedRoomQRCode;

// Usage examples:
// <AdvancedRoomQRCode 
//   roomNumber="237"
//   tenantSubdomain="demo"
//   hotelName="Grand Plaza Hotel"
//   logoUrl="/hotel-logo.png"
//   primaryColor="#1e40af"
//   style="modern"
// />