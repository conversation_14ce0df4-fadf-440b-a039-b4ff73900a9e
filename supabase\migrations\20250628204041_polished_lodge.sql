/*
  # Header Content Management

  1. New Tables
    - `header_content`
      - `id` (uuid, primary key)
      - `title` (text) - Main headline
      - `subtitle` (text) - Supporting text
      - `background_color` (text) - Hex color for background
      - `text_color` (text) - Hex color for text
      - `is_active` (boolean) - Whether to show this content
      - `created_at` (timestamp)
      - `updated_at` (timestamp)

  2. Security
    - Enable RLS on `header_content` table
    - Add policy for authenticated users to manage content
    - Add policy for public users to read active content

  3. Initial Data
    - Insert default welcome header content
*/

CREATE TABLE IF NOT EXISTS header_content (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  title text NOT NULL DEFAULT '',
  subtitle text NOT NULL DEFAULT '',
  background_color text NOT NULL DEFAULT '#1e40af',
  text_color text NOT NULL DEFAULT '#ffffff',
  is_active boolean NOT NULL DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
);

ALTER TABLE header_content ENABLE ROW LEVEL SECURITY;

-- Policy for public users to read active header content
CREATE POLICY "Anyone can read active header content"
  ON header_content
  FOR SELECT
  USING (is_active = true);

-- Policy for authenticated users to manage all header content
CREATE POLICY "Authenticated users can manage header content"
  ON header_content
  FOR ALL
  TO authenticated
  USING (true)
  WITH CHECK (true);

-- Insert default header content
INSERT INTO header_content (title, subtitle, background_color, text_color, is_active) VALUES
  ('Welcome to Riverside Inn', 'Experience luxury hospitality with cutting-edge automation', '#1e40af', '#ffffff', true);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_header_content_updated_at
    BEFORE UPDATE ON header_content
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();