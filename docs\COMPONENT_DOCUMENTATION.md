# Component Documentation

## Overview

This document provides comprehensive documentation for all React components in the HospitalityFlows application. Components are organized by category and include props, usage examples, and integration patterns.

## Component Categories

- [Core Components](#core-components) - Main application components
- [QR Code Components](#qr-code-components) - QR code generation and display
- [Guest Components](#guest-components) - Guest-facing interface components
- [Admin Components](#admin-components) - Administrative interface components
- [Staff Components](#staff-components) - Staff dashboard components
- [Context Components](#context-components) - React context providers

## Core Components

### Navigation

**File:** `src/components/Navigation.tsx`

Main navigation component for the application.

```tsx
interface NavigationProps {
  // No props - uses internal navigation items
}
```

**Usage:**
```tsx
import Navigation from '../components/Navigation';

<Navigation />
```

**Features:**
- Responsive navigation bar
- Hotel branding with icon
- Navigation items (Home, Rooms, Services, Contact)
- Active route highlighting

### TenantDebugger

**File:** `src/components/TenantDebugger.tsx`

Development-only component for debugging tenant information.

```tsx
interface TenantDebuggerProps {
  // No props - automatically detects current tenant
}
```

**Usage:**
```tsx
import TenantDebugger from '../components/TenantDebugger';

<TenantDebugger />
```

**Features:**
- Only visible in development mode
- Shows current tenant information
- Quick links to switch between tenants
- Fixed position in bottom-right corner

## QR Code Components

### RoomQRCode

**File:** `src/components/RoomQRCode.tsx`

Basic QR code component for room access.

```tsx
interface RoomQRCodeProps {
  roomNumber: string;
  tenantSubdomain: string;
  size?: number;
  level?: 'L' | 'M' | 'Q' | 'H';
  marginSize?: number;
  bgColor?: string;
  fgColor?: string;
  imageSettings?: {
    src: string;
    height: number;
    width: number;
    excavate: boolean;
    x?: number;
    y?: number;
    opacity?: number;
    crossOrigin?: 'anonymous' | 'use-credentials' | '' | undefined;
  };
}
```

**Usage:**
```tsx
import RoomQRCode from '../components/RoomQRCode';

<RoomQRCode 
  roomNumber="237"
  tenantSubdomain="demo"
  size={160}
  level="H"
  marginSize={4}
/>
```

**Features:**
- Generates QR codes for room URLs
- Customizable size and error correction
- Optional logo embedding
- High-quality SVG output

### AdvancedRoomQRCode

**File:** `src/components/AdvancedRoomQRCode.tsx`

Enhanced QR code component with styling and branding options.

```tsx
interface AdvancedRoomQRCodeProps {
  roomNumber: string;
  tenantSubdomain: string;
  hotelName: string;
  logoUrl?: string;
  size?: number;
  primaryColor?: string;
  style?: 'modern' | 'classic' | 'minimal';
}
```

**Usage:**
```tsx
import AdvancedRoomQRCode from '../components/AdvancedRoomQRCode';

<AdvancedRoomQRCode 
  roomNumber="237"
  tenantSubdomain="demo"
  hotelName="Grand Plaza Hotel"
  logoUrl="/hotel-logo.png"
  primaryColor="#1e40af"
  style="modern"
/>
```

**Features:**
- Three style variants (modern, classic, minimal)
- Hotel branding integration
- Logo embedding with proper positioning
- Customizable colors and sizing

### PrintableRoomCard

**File:** `src/components/PrintableRoomCard.tsx`

Complete printable room card with QR code and hotel information.

```tsx
interface PrintableRoomCardProps {
  roomNumber: string;
  tenantSubdomain: string;
  hotelName: string;
  hotelLogo?: string;
  wifiName?: string;
  wifiPassword?: string;
  frontDeskPhone?: string;
  primaryColor?: string;
}
```

**Usage:**
```tsx
import PrintableRoomCard from '../components/PrintableRoomCard';

<PrintableRoomCard
  roomNumber="237"
  tenantSubdomain="demo"
  hotelName="Grand Plaza Hotel"
  hotelLogo="/hotel-logo.png"
  wifiName="GrandPlaza_Guest"
  wifiPassword="Welcome2025"
  frontDeskPhone="Dial 0"
  primaryColor="#1e40af"
/>
```

**Features:**
- Print-optimized layout
- WiFi credentials display
- Contact information
- QR code with instructions
- Hotel branding

## Guest Components

### AnnouncementBar

**File:** `src/components/guest/AnnouncementBar.tsx`

Displays active announcements to guests.

```tsx
interface AnnouncementBarProps {
  announcements: GuestAnnouncement[];
  maxDisplay?: number;
}
```

**Usage:**
```tsx
import AnnouncementBar from '../components/guest/AnnouncementBar';

<AnnouncementBar 
  announcements={announcements}
  maxDisplay={3}
/>
```

**Features:**
- Multiple announcement types with icons
- Customizable display limit
- Action buttons for announcements
- Responsive design

### ServiceCard

**File:** `src/components/guest/ServiceCard.tsx`

Interactive card for service selection.

```tsx
interface ServiceConfig {
  title: string;
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  buttonColor: string;
  options: string[];
}

interface ServiceCardProps {
  serviceKey: string;
  service: ServiceConfig;
  onClick: () => void;
}
```

**Usage:**
```tsx
import ServiceCard from '../components/guest/ServiceCard';

<ServiceCard
  serviceKey="maintenance"
  service={maintenanceService}
  onClick={() => setActiveService('maintenance')}
/>
```

**Features:**
- Hover animations
- Service-specific descriptions
- Icon and color customization
- Click handling for service selection

### ServiceRequestForm

**File:** `src/components/guest/ServiceRequestForm.tsx`

Form for submitting service requests.

```tsx
interface RequestForm {
  service_type: string;
  description: string;
  urgency: string;
  guest_name: string;
  guest_contact: string;
}

interface ServiceRequestFormProps {
  serviceKey: string;
  service: ServiceConfig;
  roomNumber: string;
  onSubmit: (data: RequestForm) => void;
  onCancel: () => void;
}
```

**Usage:**
```tsx
import ServiceRequestForm from '../components/guest/ServiceRequestForm';

<ServiceRequestForm
  serviceKey="maintenance"
  service={maintenanceService}
  roomNumber="237"
  onSubmit={handleSubmit}
  onCancel={handleCancel}
/>
```

**Features:**
- Dynamic form fields based on service type
- Input validation
- Guest information pre-population
- Urgency level selection

### GuestInfoSection

**File:** `src/components/guest/GuestInfoSection.tsx`

Editable guest information display.

```tsx
interface GuestInfoProps {
  phone: string;
  email: string;
  isEditingPhone: boolean;
  isEditingEmail: boolean;
  onPhoneChange: (value: string) => void;
  onEmailChange: (value: string) => void;
  onEditPhone: () => void;
  onEditEmail: () => void;
  onPhoneEditComplete: () => void;
  onEmailEditComplete: () => void;
}
```

**Usage:**
```tsx
import GuestInfoSection from '../components/guest/GuestInfoSection';

<GuestInfoSection
  phone={phone}
  email={email}
  isEditingPhone={isEditingPhone}
  isEditingEmail={isEditingEmail}
  onPhoneChange={setPhone}
  onEmailChange={setEmail}
  onEditPhone={() => setIsEditingPhone(true)}
  onEditEmail={() => setIsEditingEmail(true)}
  onPhoneEditComplete={() => setIsEditingPhone(false)}
  onEmailEditComplete={() => setIsEditingEmail(false)}
/>
```

**Features:**
- Inline editing for phone and email
- PMS integration indicator
- Keyboard navigation support
- Auto-save on blur/enter

## Admin Components

### AnnouncementManagement

**File:** `src/components/admin/AnnouncementManagement.tsx`

Complete announcement management interface.

```tsx
interface AnnouncementForm {
  headline: string;
  content: string;
  announcement_type: string;
  background_color: string;
  text_color: string;
  start_date: string;
  end_date: string | null;
  image_url: string | null;
  action_url: string | null;
  action_text: string | null;
}

interface AnnouncementManagementProps {
  announcements: GuestAnnouncement[];
  onCreateAnnouncement: (data: AnnouncementForm) => void;
  onUpdateAnnouncement: (id: string, data: Partial<AnnouncementForm>) => void;
  onDeleteAnnouncement: (id: string) => void;
}
```

**Usage:**
```tsx
import AnnouncementManagement from '../components/admin/AnnouncementManagement';

<AnnouncementManagement
  announcements={announcements}
  onCreateAnnouncement={handleCreate}
  onUpdateAnnouncement={handleUpdate}
  onDeleteAnnouncement={handleDelete}
/>
```

**Features:**
- Create/edit/delete announcements
- Color picker for styling
- Date scheduling
- Preview functionality
- Drag-and-drop reordering

### QRCodeGenerator

**File:** `src/components/admin/QRCodeGenerator.tsx`

Bulk QR code generation tool.

```tsx
interface QRCodeGeneratorProps {
  defaultRooms?: string[];
}
```

**Usage:**
```tsx
import QRCodeGenerator from '../components/admin/QRCodeGenerator';

<QRCodeGenerator defaultRooms={['101', '102', '103']} />
```

**Features:**
- Add/remove room numbers
- Download individual QR codes as PNG
- Print all QR codes at once
- Tenant-specific URL generation

### ActiveAnnouncementsSummary

**File:** `src/components/admin/ActiveAnnouncementsSummary.tsx`

Summary view of active announcements.

```tsx
interface ActiveAnnouncementsSummaryProps {
  announcements: GuestAnnouncement[];
}
```

**Usage:**
```tsx
import ActiveAnnouncementsSummary from '../components/admin/ActiveAnnouncementsSummary';

<ActiveAnnouncementsSummary announcements={activeAnnouncements} />
```

**Features:**
- Quick overview of active announcements
- Status indicators
- Direct links to edit announcements

## Staff Components

### LiveActivityFeed

**File:** `src/components/staff/LiveActivityFeed.tsx`

Real-time activity monitoring for staff.

```tsx
interface LiveActivityFeedProps {
  // No props - uses tenant configuration
}
```

**Usage:**
```tsx
import LiveActivityFeed from '../components/staff/LiveActivityFeed';

<LiveActivityFeed />
```

**Features:**
- Real-time request updates
- Room status monitoring
- Activity timeline
- Tenant-specific data filtering

## Context Components

### ToastContext

**File:** `src/contexts/ToastContext.tsx`

Toast notification system.

```tsx
interface ToastContextType {
  showToast: (message: string, type: 'success' | 'error' | 'info') => void;
}

interface ToastProviderProps {
  children: React.ReactNode;
}
```

**Usage:**
```tsx
import { ToastProvider, useToast } from '../contexts/ToastContext';

// Provider
<ToastProvider>
  <App />
</ToastProvider>

// Consumer
const { showToast } = useToast();
showToast('Success!', 'success');
```

**Features:**
- Multiple toast types
- Auto-dismiss functionality
- Queue management
- Accessible notifications

## Component Integration Patterns

### Tenant-Aware Components

Most components integrate with the tenant system:

```tsx
import { getCurrentTenant } from '../lib/tenant-config';

const MyComponent = () => {
  const tenant = getCurrentTenant();
  
  // Use tenant configuration
  const primaryColor = tenant.branding?.primaryColor || '#1e40af';
  
  return (
    <div style={{ color: primaryColor }}>
      {tenant.name}
    </div>
  );
};
```

### Database Integration

Components that interact with Supabase:

```tsx
import { supabase } from '../lib/supabase';
import { useToast } from '../contexts/ToastContext';

const DatabaseComponent = () => {
  const { showToast } = useToast();
  
  const fetchData = async () => {
    try {
      const { data, error } = await supabase
        .from('table_name')
        .select('*');
        
      if (error) throw error;
      
      // Handle data
    } catch (error) {
      showToast('Error loading data', 'error');
    }
  };
};
```

### Feature Toggle Integration

Components that respect feature toggles:

```tsx
import { isFeatureEnabled } from '../lib/tenant-config';

const ConditionalComponent = () => {
  if (!isFeatureEnabled('maintenance')) {
    return null;
  }
  
  return <MaintenanceForm />;
};
```

## Styling Guidelines

### Tailwind CSS Classes

Components use consistent Tailwind CSS patterns:

```tsx
// Card styling
className="bg-white rounded-lg shadow-lg p-6"

// Button styling
className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"

// Input styling
className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
```

### Responsive Design

All components follow mobile-first responsive design:

```tsx
className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
```

### Color System

Components use tenant-aware colors:

```tsx
const tenant = getCurrentTenant();
const primaryColor = tenant.branding?.primaryColor || '#1e40af';

// Apply as inline style or CSS variable
style={{ backgroundColor: primaryColor }}
```

## Testing Components

### Component Testing

Example test structure:

```tsx
import { render, screen } from '@testing-library/react';
import { ToastProvider } from '../contexts/ToastContext';
import MyComponent from './MyComponent';

const renderWithProviders = (component: React.ReactElement) => {
  return render(
    <ToastProvider>
      {component}
    </ToastProvider>
  );
};

test('renders component correctly', () => {
  renderWithProviders(<MyComponent />);
  expect(screen.getByText('Expected Text')).toBeInTheDocument();
});
```

### Integration Testing

Test components with real data:

```tsx
test('handles service request submission', async () => {
  const mockSubmit = jest.fn();
  
  renderWithProviders(
    <ServiceRequestForm
      serviceKey="maintenance"
      service={mockService}
      roomNumber="237"
      onSubmit={mockSubmit}
      onCancel={() => {}}
    />
  );
  
  // Simulate user interaction
  // Assert expected behavior
});
```

## Performance Considerations

### Memoization

Use React.memo for expensive components:

```tsx
const ExpensiveComponent = React.memo(({ data }) => {
  // Expensive rendering logic
  return <div>{/* rendered content */}</div>;
});
```

### Lazy Loading

Implement lazy loading for large components:

```tsx
const LazyComponent = React.lazy(() => import('./HeavyComponent'));

// Usage
<Suspense fallback={<div>Loading...</div>}>
  <LazyComponent />
</Suspense>
```

### Virtual Scrolling

For large lists, consider virtual scrolling:

```tsx
// Use libraries like react-window for large datasets
import { FixedSizeList as List } from 'react-window';
```

## Accessibility Guidelines

### ARIA Labels

Provide proper ARIA labels:

```tsx
<button
  aria-label="Submit maintenance request"
  onClick={handleSubmit}
>
  Submit
</button>
```

### Keyboard Navigation

Ensure keyboard accessibility:

```tsx
<div
  tabIndex={0}
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleClick();
    }
  }}
>
  Interactive Element
</div>
```

### Screen Reader Support

Use semantic HTML and proper headings:

```tsx
<main>
  <h1>Page Title</h1>
  <section>
    <h2>Section Title</h2>
    <p>Content</p>
  </section>
</main>
```

## Component Development Best Practices

### Props Interface

Always define TypeScript interfaces:

```tsx
interface ComponentProps {
  required: string;
  optional?: number;
  callback: (value: string) => void;
}
```

### Default Props

Use default parameters:

```tsx
const Component: React.FC<ComponentProps> = ({
  required,
  optional = 10,
  callback
}) => {
  // Component logic
};
```

### Error Boundaries

Wrap components in error boundaries:

```tsx
class ErrorBoundary extends React.Component {
  // Error boundary implementation
}

// Usage
<ErrorBoundary>
  <MyComponent />
</ErrorBoundary>
```

### Documentation

Document complex components:

```tsx
/**
 * ServiceCard component for displaying available hotel services
 * 
 * @param serviceKey - Unique identifier for the service
 * @param service - Service configuration object
 * @param onClick - Callback when service is selected
 */
const ServiceCard: React.FC<ServiceCardProps> = ({ ... }) => {
  // Implementation
};
```

## Additional Resources

- [React Documentation](https://reactjs.org/docs)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS Documentation](https://tailwindcss.com/docs)
- [Lucide React Icons](https://lucide.dev/guide/packages/lucide-react)

---

For questions about specific components or to request new component documentation, please contact the development team or refer to the source code in the `src/components/` directory.
