<?xml version="1.0" encoding="UTF-8" ?>
<project name="PostgreSQL" database="PostgreSQL" id="8f7b44b9-575c-46d0-a3ad-32cd630b6dc1" >
	<schema name="public" catalogname="postgres" >
		<comment><![CDATA[standard public schema]]></comment>
		<table name="api_credentials" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="service_name" type="varchar" length="100" jt="12" mandatory="y" />
			<column name="credentials_encrypted" type="text" jt="12" mandatory="y" />
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="last_verified_at" type="timestamptz" jt="93" />
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="api_credentials_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_api_credentials_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="api_credentials_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="communication_channels" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="channel_type" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="channel_identifier" type="varchar" length="255" jt="12" mandatory="y" />
			<column name="channel_name" type="varchar" length="100" jt="12" />
			<column name="is_primary" type="boolean" jt="16" >
				<defo><![CDATA[false]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="metadata" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="communication_channels_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_communication_channels_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="communication_channels_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="departments" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="name" type="varchar" length="100" jt="12" mandatory="y" />
			<column name="code" type="varchar" length="20" jt="12" mandatory="y" />
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="departments_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="departments_tenant_id_code_key" unique="UNIQUE_KEY" >
				<column name="tenant_id" />
				<column name="code" />
			</index>
			<index name="idx_departments_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="departments_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="device_departments" row_count="0" spec="" >
			<column name="device_id" type="uuid" jt="102" mandatory="y" />
			<column name="department_id" type="uuid" jt="102" mandatory="y" />
			<index name="device_departments_pkey" unique="PRIMARY_KEY" >
				<column name="device_id" />
				<column name="department_id" />
			</index>
			<fk name="device_departments_device_id_fkey" to_schema="postgres.public" to_table="devices" delete_action="cascade" options="" >
				<fk_column name="device_id" pk="id" />
			</fk>
			<fk name="device_departments_department_id_fkey" to_schema="postgres.public" to_table="departments" delete_action="cascade" options="" >
				<fk_column name="department_id" pk="id" />
			</fk>
		</table>
		<table name="device_sessions" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="device_id" type="uuid" jt="102" mandatory="y" />
			<column name="user_id" type="uuid" jt="102" mandatory="y" />
			<column name="login_time" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="logout_time" type="timestamptz" jt="93" />
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<index name="device_sessions_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_device_sessions_device" unique="NORMAL" spec="USING  btree" >
				<column name="device_id" />
			</index>
			<index name="idx_device_sessions_user" unique="NORMAL" spec="USING  btree" >
				<column name="user_id" />
			</index>
			<index name="idx_device_sessions_active" unique="NORMAL" spec="USING  btree" options="WHERE (is_active = true)" >
				<column name="is_active" />
			</index>
			<fk name="device_sessions_device_id_fkey" to_schema="postgres.public" to_table="devices" delete_action="cascade" options="" >
				<fk_column name="device_id" pk="id" />
			</fk>
			<fk name="device_sessions_user_id_fkey" to_schema="postgres.public" to_table="users" delete_action="cascade" options="" >
				<fk_column name="user_id" pk="id" />
			</fk>
		</table>
		<table name="devices" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="device_name" type="varchar" length="100" jt="12" mandatory="y" />
			<column name="device_type" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="device_identifier" type="varchar" length="255" jt="12" />
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="devices_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_devices_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="devices_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="guest_announcements" row_count="1" spec="" >
			<comment><![CDATA[Stores dynamic content/announcements that hotels can display on their guest portal. 
Examples: dinner specials, events, pool closures, happy hour promotions, etc.]]></comment>
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[gen_random_uuid()]]></defo>
			</column>
			<column name="headline" type="text" jt="12" mandatory="y" >
				<defo><![CDATA[''::text]]></defo>
			</column>
			<column name="content" type="text" jt="12" mandatory="y" >
				<defo><![CDATA[''::text]]></defo>
			</column>
			<column name="background_color" type="text" jt="12" mandatory="y" >
				<defo><![CDATA['#1e40af'::text]]></defo>
			</column>
			<column name="text_color" type="text" jt="12" mandatory="y" >
				<defo><![CDATA['#ffffff'::text]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" mandatory="y" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="metadata" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
				<column name="days" type="jsonb_array" jt="5002" content_type="JSON_ARRAY" virtual="y" />
				<column name="recurring" type="boolean" jt="16" virtual="y" />
				<column name="affected_area" type="text" jt="12" virtual="y" />
				<column name="time" type="text" jt="12" virtual="y" />
				<column name="location" type="text" jt="12" virtual="y" />
			</column>
			<column name="announcement_type" type="varchar" jt="12" >
				<defo><![CDATA['general'::character varying]]></defo>
			</column>
			<column name="display_order" type="integer" length="32" jt="4" >
				<defo><![CDATA[0]]></defo>
			</column>
			<column name="start_date" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="end_date" type="timestamptz" jt="93" />
			<column name="image_url" type="text" jt="12" />
			<column name="action_url" type="text" jt="12" />
			<column name="action_text" type="varchar" length="100" jt="12" />
			<index name="header_content_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_header_content_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<index name="idx_guest_announcements_active_dates" unique="NORMAL" spec="USING  btree" options="WHERE (is_active = true)" >
				<column name="tenant_id" />
				<column name="start_date" />
				<column name="end_date" />
			</index>
			<fk name="header_content_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="message_logs" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="request_id" type="uuid" jt="102" />
			<column name="channel_type" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="direction" type="varchar" length="20" jt="12" mandatory="y" />
			<column name="from_identifier" type="varchar" length="255" jt="12" />
			<column name="to_identifier" type="varchar" length="255" jt="12" />
			<column name="message_content" type="text" jt="12" />
			<column name="status" type="varchar" jt="12" >
				<defo><![CDATA['pending'::character varying]]></defo>
			</column>
			<column name="metadata" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="message_logs_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_message_logs_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<index name="idx_message_logs_request" unique="NORMAL" spec="USING  btree" >
				<column name="request_id" />
			</index>
			<fk name="message_logs_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
			<fk name="message_logs_request_id_fkey" to_schema="postgres.public" to_table="requests" options="" >
				<fk_column name="request_id" pk="id" />
			</fk>
		</table>
		<table name="message_templates" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="template_code" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="template_name" type="varchar" length="100" jt="12" mandatory="y" />
			<column name="channel_type" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="subject" type="varchar" length="255" jt="12" />
			<column name="body" type="text" jt="12" mandatory="y" />
			<column name="variables" type="jsonb" jt="2000" >
				<defo><![CDATA['[]'::jsonb]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="message_templates_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="message_templates_tenant_id_template_code_key" unique="UNIQUE_KEY" >
				<column name="tenant_id" />
				<column name="template_code" />
			</index>
			<index name="idx_message_templates_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="message_templates_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="request_actions" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="request_id" type="uuid" jt="102" mandatory="y" />
			<column name="device_id" type="uuid" jt="102" />
			<column name="user_id" type="uuid" jt="102" />
			<column name="action_type" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="action_data" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="request_actions_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_request_actions_request" unique="NORMAL" spec="USING  btree" >
				<column name="request_id" />
			</index>
			<fk name="request_actions_request_id_fkey" to_schema="postgres.public" to_table="requests" delete_action="cascade" options="" >
				<fk_column name="request_id" pk="id" />
			</fk>
			<fk name="request_actions_device_id_fkey" to_schema="postgres.public" to_table="devices" options="" >
				<fk_column name="device_id" pk="id" />
			</fk>
			<fk name="request_actions_user_id_fkey" to_schema="postgres.public" to_table="users" options="" >
				<fk_column name="user_id" pk="id" />
			</fk>
		</table>
		<table name="request_assignments" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="request_id" type="uuid" jt="102" mandatory="y" />
			<column name="device_id" type="uuid" jt="102" mandatory="y" />
			<column name="assigned_by_user_id" type="uuid" jt="102" />
			<column name="assigned_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="unassigned_at" type="timestamptz" jt="93" />
			<column name="reason" type="varchar" length="255" jt="12" />
			<index name="request_assignments_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<fk name="request_assignments_request_id_fkey" to_schema="postgres.public" to_table="requests" delete_action="cascade" options="" >
				<fk_column name="request_id" pk="id" />
			</fk>
			<fk name="request_assignments_device_id_fkey" to_schema="postgres.public" to_table="devices" options="" >
				<fk_column name="device_id" pk="id" />
			</fk>
			<fk name="request_assignments_assigned_by_user_id_fkey" to_schema="postgres.public" to_table="users" options="" >
				<fk_column name="assigned_by_user_id" pk="id" />
			</fk>
		</table>
		<table name="request_attachments" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="request_id" type="uuid" jt="102" mandatory="y" />
			<column name="file_name" type="varchar" length="255" jt="12" mandatory="y" />
			<column name="file_url" type="text" jt="12" mandatory="y" />
			<column name="file_type" type="varchar" length="50" jt="12" />
			<column name="file_size" type="integer" length="32" jt="4" />
			<column name="uploaded_by_user_id" type="uuid" jt="102" />
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="request_attachments_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_request_attachments_request" unique="NORMAL" spec="USING  btree" >
				<column name="request_id" />
			</index>
			<fk name="request_attachments_request_id_fkey" to_schema="postgres.public" to_table="requests" delete_action="cascade" options="" >
				<fk_column name="request_id" pk="id" />
			</fk>
			<fk name="request_attachments_uploaded_by_user_id_fkey" to_schema="postgres.public" to_table="users" options="" >
				<fk_column name="uploaded_by_user_id" pk="id" />
			</fk>
		</table>
		<table name="request_types" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="department_id" type="uuid" jt="102" mandatory="y" />
			<column name="name" type="varchar" length="100" jt="12" mandatory="y" />
			<column name="code" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="priority_level" type="integer" length="32" jt="4" >
				<defo><![CDATA[3]]></defo>
			</column>
			<column name="sla_minutes" type="integer" length="32" jt="4" >
				<defo><![CDATA[120]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<index name="request_types_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="request_types_tenant_id_code_key" unique="UNIQUE_KEY" >
				<column name="tenant_id" />
				<column name="code" />
			</index>
			<index name="idx_request_types_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="request_types_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
			<fk name="request_types_department_id_fkey" to_schema="postgres.public" to_table="departments" delete_action="cascade" options="" >
				<fk_column name="department_id" pk="id" />
			</fk>
		</table>
		<table name="requests" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="request_number" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="request_type_id" type="uuid" jt="102" mandatory="y" />
			<column name="room_number" type="varchar" length="20" jt="12" />
			<column name="guest_name" type="varchar" length="255" jt="12" />
			<column name="guest_contact" type="varchar" length="255" jt="12" />
			<column name="description" type="text" jt="12" mandatory="y" />
			<column name="status" type="varchar" jt="12" >
				<defo><![CDATA['new'::character varying]]></defo>
			</column>
			<column name="priority_level" type="integer" length="32" jt="4" >
				<defo><![CDATA[3]]></defo>
			</column>
			<column name="source" type="varchar" jt="12" >
				<defo><![CDATA['guest'::character varying]]></defo>
			</column>
			<column name="assigned_device_id" type="uuid" jt="102" />
			<column name="metadata" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="assigned_at" type="timestamptz" jt="93" />
			<column name="completed_at" type="timestamptz" jt="93" />
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="requests_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="idx_requests_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<index name="idx_requests_status" unique="NORMAL" spec="USING  btree" >
				<column name="status" />
			</index>
			<index name="idx_requests_assigned_device" unique="NORMAL" spec="USING  btree" >
				<column name="assigned_device_id" />
			</index>
			<index name="idx_requests_created" unique="NORMAL" spec="USING  btree" >
				<column name="created_at" options=" DESC" />
			</index>
			<index name="idx_requests_number" unique="NORMAL" spec="USING  btree" >
				<column name="request_number" />
			</index>
			<index name="idx_requests_number_tenant" unique="UNIQUE_INDEX" spec="USING  btree" >
				<column name="tenant_id" />
				<column name="request_number" />
			</index>
			<fk name="requests_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
			<fk name="requests_request_type_id_fkey" to_schema="postgres.public" to_table="request_types" options="" >
				<fk_column name="request_type_id" pk="id" />
			</fk>
			<fk name="requests_assigned_device_id_fkey" to_schema="postgres.public" to_table="devices" options="" >
				<fk_column name="assigned_device_id" pk="id" />
			</fk>
		</table>
		<table name="rooms" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="room_number" type="varchar" length="20" jt="12" mandatory="y" />
			<column name="room_type" type="varchar" length="50" jt="12" />
			<column name="floor" type="varchar" length="10" jt="12" />
			<column name="building" type="varchar" length="50" jt="12" />
			<column name="status" type="varchar" jt="12" >
				<defo><![CDATA['clean'::character varying]]></defo>
			</column>
			<column name="pms_room_id" type="varchar" length="100" jt="12" />
			<column name="metadata" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="rooms_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="rooms_tenant_id_room_number_key" unique="UNIQUE_KEY" >
				<column name="tenant_id" />
				<column name="room_number" />
			</index>
			<index name="idx_rooms_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<index name="idx_rooms_status" unique="NORMAL" spec="USING  btree" >
				<column name="status" />
			</index>
			<fk name="rooms_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="tenants" row_count="1" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="name" type="varchar" length="255" jt="12" mandatory="y" />
			<column name="subdomain" type="varchar" length="100" jt="12" />
			<column name="subscription_tier" type="varchar" jt="12" >
				<defo><![CDATA['starter'::character varying]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="settings" type="jsonb" jt="2000" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="tenants_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="tenants_subdomain_key" unique="UNIQUE_KEY" >
				<column name="subdomain" />
			</index>
			<index name="idx_tenants_subdomain" unique="NORMAL" spec="USING  btree" options="WHERE (is_active = true)" >
				<column name="subdomain" />
			</index>
		</table>
		<table name="users" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="email" type="varchar" length="255" jt="12" mandatory="y" />
			<column name="name" type="varchar" length="255" jt="12" mandatory="y" />
			<column name="role" type="varchar" jt="12" >
				<defo><![CDATA['staff'::character varying]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="users_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="users_tenant_id_email_key" unique="UNIQUE_KEY" >
				<column name="tenant_id" />
				<column name="email" />
			</index>
			<index name="idx_users_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<index name="idx_users_email" unique="NORMAL" spec="USING  btree" >
				<column name="email" />
			</index>
			<fk name="users_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<table name="workflows" row_count="0" spec="" >
			<column name="id" type="uuid" jt="102" mandatory="y" >
				<defo><![CDATA[uuid_generate_v4()]]></defo>
			</column>
			<column name="tenant_id" type="uuid" jt="102" mandatory="y" />
			<column name="workflow_name" type="varchar" length="100" jt="12" mandatory="y" />
			<column name="workflow_code" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="trigger_type" type="varchar" length="50" jt="12" mandatory="y" />
			<column name="configuration" type="jsonb" jt="2000" mandatory="y" >
				<defo><![CDATA['{}'::jsonb]]></defo>
			</column>
			<column name="is_active" type="boolean" jt="16" >
				<defo><![CDATA[true]]></defo>
			</column>
			<column name="created_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<column name="updated_at" type="timestamptz" jt="93" >
				<defo><![CDATA[now()]]></defo>
			</column>
			<index name="workflows_pkey" unique="PRIMARY_KEY" >
				<column name="id" />
			</index>
			<index name="workflows_tenant_id_workflow_code_key" unique="UNIQUE_KEY" >
				<column name="tenant_id" />
				<column name="workflow_code" />
			</index>
			<index name="idx_workflows_tenant" unique="NORMAL" spec="USING  btree" >
				<column name="tenant_id" />
			</index>
			<fk name="workflows_tenant_id_fkey" to_schema="postgres.public" to_table="tenants" delete_action="cascade" options="" >
				<fk_column name="tenant_id" pk="id" />
			</fk>
		</table>
		<view name="active_guest_announcements" >
			<view_script><![CDATA[CREATE OR REPLACE VIEW active_guest_announcements AS SELECT ${view},
    ga.headline,
    ga.content,
    ga.background_color,
    ga.text_color,
    ga.is_active,
    ga.created_at,
    ga.updated_at,
    ga.tenant_id,
    ga.metadata,
    ga.announcement_type,
    ga.display_order,
    ga.start_date,
    ga.end_date,
    ga.image_url,
    ga.action_url,
    ga.action_text,
    t.name AS tenant_name,
    t.subdomain
   FROM (guest_announcements ga
     JOIN tenants t ON ((ga.tenant_id = t.id)))
  WHERE ((ga.is_active = true) AND (ga.start_date <= now()) AND ((ga.end_date IS NULL) OR (ga.end_date > now())))
  ORDER BY ga.display_order, ga.created_at DESC]]></view_script>
			<column name="id" type="uuid" />
			<column name="headline" type="text" />
			<column name="content" type="text" />
			<column name="background_color" type="text" />
			<column name="text_color" type="text" />
			<column name="is_active" type="boolean" />
			<column name="created_at" type="timestamptz" />
			<column name="updated_at" type="timestamptz" />
			<column name="tenant_id" type="uuid" />
			<column name="metadata" type="jsonb" />
			<column name="announcement_type" type="varchar" />
			<column name="display_order" type="integer" />
			<column name="start_date" type="timestamptz" />
			<column name="end_date" type="timestamptz" />
			<column name="image_url" type="text" />
			<column name="action_url" type="text" />
			<column name="action_text" type="varchar" />
			<column name="tenant_name" type="varchar" />
			<column name="subdomain" type="varchar" />
		</view>
		<view name="announcement_management" >
			<view_script><![CDATA[CREATE OR REPLACE VIEW announcement_management AS SELECT ${view},
    ga.tenant_id,
    t.name AS tenant_name,
    ga.headline,
    ga.content,
    ga.announcement_type,
    ga.display_order,
    ga.is_active,
    ga.start_date,
    ga.end_date,
        CASE
            WHEN (ga.is_active = false) THEN 'Inactive'::text
            WHEN (now() < ga.start_date) THEN 'Scheduled'::text
            WHEN ((ga.end_date IS NOT NULL) AND (now() > ga.end_date)) THEN 'Expired'::text
            ELSE 'Active'::text
        END AS status,
    ga.created_at,
    ga.updated_at
   FROM (guest_announcements ga
     JOIN tenants t ON ((ga.tenant_id = t.id)))
  ORDER BY ga.tenant_id, ga.display_order, ga.created_at DESC]]></view_script>
			<column name="id" type="uuid" />
			<column name="tenant_id" type="uuid" />
			<column name="tenant_name" type="varchar" />
			<column name="headline" type="text" />
			<column name="content" type="text" />
			<column name="announcement_type" type="varchar" />
			<column name="display_order" type="integer" />
			<column name="is_active" type="boolean" />
			<column name="start_date" type="timestamptz" />
			<column name="end_date" type="timestamptz" />
			<column name="status" type="text" />
			<column name="created_at" type="timestamptz" />
			<column name="updated_at" type="timestamptz" />
		</view>
		<function name="create_announcement" id="7b064ba5-6abb-4d88-8fa5-d3e2f6941bae" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.create_announcement(p_tenant_id uuid, p_headline text, p_content text, p_type character varying DEFAULT 'general'::character varying, p_options jsonb DEFAULT '{}'::jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_announcement_id UUID;
    v_max_order INTEGER;
BEGIN
    -- Get the max display order for this tenant
    SELECT COALESCE(MAX(display_order), -1) + 1
    INTO v_max_order
    FROM guest_announcements
    WHERE tenant_id = p_tenant_id;
    
    INSERT INTO guest_announcements (
        tenant_id,
        headline,
        content,
        announcement_type,
        background_color,
        text_color,
        image_url,
        action_url,
        action_text,
        display_order,
        start_date,
        end_date,
        metadata
    ) VALUES (
        p_tenant_id,
        p_headline,
        p_content,
        p_type,
        COALESCE(p_options->>'background_color', 
            CASE p_type
                WHEN 'alert' THEN '#dc2626'
                WHEN 'dining' THEN '#059669'
                WHEN 'event' THEN '#1e3a8a'
                WHEN 'promotion' THEN '#7c3aed'
                ELSE '#3b82f6'
            END
        ),
        COALESCE(p_options->>'text_color', '#ffffff'),
        p_options->>'image_url',
        p_options->>'action_url',
        p_options->>'action_text',
        COALESCE((p_options->>'display_order')::INTEGER, v_max_order),
        COALESCE((p_options->>'start_date')::TIMESTAMPTZ, NOW()),
        (p_options->>'end_date')::TIMESTAMPTZ,
        COALESCE(p_options->'metadata', '{}')::jsonb
    ) RETURNING id INTO v_announcement_id;
    
    RETURN v_announcement_id;
END;
$function$
;]]></string>
			<comment><![CDATA[Create a new guest announcement. Example usage:
SELECT create_announcement(
    'tenant-uuid',
    'Tonight: Italian Night Buffet',
    'All-you-can-eat Italian buffet featuring homemade pasta, pizza, and tiramisu. $29.99 per person.',
    'dining',
    '{
        "action_text": "Reserve Now",
        "action_url": "/reservations",
        "end_date": "2024-12-25T23:00:00Z",
        "metadata": {"price": "$29.99", "location": "Main Restaurant"}
    }'::jsonb
);]]></comment>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
			<input_param name="p_headline" jt="-1" type="text" inOut="1" />
			<input_param name="p_content" jt="-1" type="text" inOut="1" />
			<input_param name="p_type" jt="-1" type="character varying" inOut="1" />
			<input_param name="p_options" jt="-1" type="jsonb" inOut="1" />
		</function>
		<function name="end_active_device_sessions" id="7c51b327-39a9-480a-97e4-d2107d70cfe9" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.end_active_device_sessions()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- End any active sessions for this device
    UPDATE device_sessions
    SET is_active = false,
        logout_time = NOW()
    WHERE device_id = NEW.device_id
    AND is_active = true
    AND id != NEW.id;
    
    RETURN NEW;
END;
$function$
;]]></string>
			<result_param jt="-1" />
		</function>
		<function name="generate_request_number" id="425a010c-e8eb-4ede-8af1-2734bfc2653d" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.generate_request_number(tenant_uuid uuid)
 RETURNS character varying
 LANGUAGE plpgsql
AS $function$
DECLARE
    current_year VARCHAR(4);
    last_number INTEGER;
    new_number VARCHAR(50);
BEGIN
    current_year := TO_CHAR(CURRENT_DATE, 'YYYY');
    
    SELECT COALESCE(MAX(CAST(SUBSTRING(request_number FROM '\d+$') AS INTEGER)), 0)
    INTO last_number
    FROM requests
    WHERE tenant_id = tenant_uuid
    AND request_number LIKE 'REQ-' || current_year || '-%';
    
    new_number := 'REQ-' || current_year || '-' || LPAD((last_number + 1)::TEXT, 4, '0');
    
    RETURN new_number;
END;
$function$
;]]></string>
			<input_param name="tenant_uuid" jt="-1" type="uuid" inOut="1" />
		</function>
		<function name="get_active_announcements" id="ac553509-f4ef-46bc-a55f-44f934838dc0" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.get_active_announcements(p_tenant_id uuid, p_announcement_type character varying DEFAULT NULL::character varying)
 RETURNS TABLE(id uuid, headline text, content text, announcement_type character varying, background_color text, text_color text, image_url text, action_url text, action_text character varying, display_order integer, metadata jsonb)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        ga.id,
        ga.headline,
        ga.content,
        ga.announcement_type,
        ga.background_color,
        ga.text_color,
        ga.image_url,
        ga.action_url,
        ga.action_text,
        ga.display_order,
        ga.metadata
    FROM guest_announcements ga
    WHERE ga.tenant_id = p_tenant_id
    AND ga.is_active = true
    AND ga.start_date <= NOW()
    AND (ga.end_date IS NULL OR ga.end_date > NOW())
    AND (p_announcement_type IS NULL OR ga.announcement_type = p_announcement_type)
    ORDER BY ga.display_order, ga.created_at DESC;
END;
$function$
;]]></string>
			<comment><![CDATA[Get active announcements for display. Examples:
-- Get all active announcements
SELECT * FROM get_active_announcements('tenant-uuid');

-- Get only dining announcements
SELECT * FROM get_active_announcements('tenant-uuid', 'dining');

-- Get only alerts
SELECT * FROM get_active_announcements('tenant-uuid', 'alert');]]></comment>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
			<input_param name="p_announcement_type" jt="-1" type="character varying" inOut="1" />
			<input_param name="id" jt="-1" type="uuid" inOut="3" />
			<input_param name="headline" jt="-1" type="text" inOut="3" />
			<input_param name="content" jt="-1" type="text" inOut="3" />
			<input_param name="announcement_type" jt="-1" type="character varying" inOut="3" />
			<input_param name="background_color" jt="-1" type="text" inOut="3" />
			<input_param name="text_color" jt="-1" type="text" inOut="3" />
			<input_param name="image_url" jt="-1" type="text" inOut="3" />
			<input_param name="action_url" jt="-1" type="text" inOut="3" />
			<input_param name="action_text" jt="-1" type="character varying" inOut="3" />
			<input_param name="display_order" jt="-1" type="integer" inOut="3" />
			<input_param name="metadata" jt="-1" type="jsonb" inOut="3" />
		</function>
		<function name="get_active_header_content" id="75da1f15-4b63-4a38-aa4e-0e037ad5f424" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.get_active_header_content(p_tenant_id uuid, p_page_type character varying DEFAULT 'guest_portal'::character varying)
 RETURNS TABLE(id uuid, title text, subtitle text, background_color text, text_color text, logo_url text, hero_image_url text, contact_phone character varying, contact_email character varying, portal_instructions text, custom_css text, metadata jsonb)
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
    RETURN QUERY
    SELECT 
        hc.id,
        hc.title,
        hc.subtitle,
        hc.background_color,
        hc.text_color,
        hc.logo_url,
        hc.hero_image_url,
        hc.contact_phone,
        hc.contact_email,
        hc.portal_instructions,
        hc.custom_css,
        hc.metadata
    FROM header_content hc
    WHERE hc.tenant_id = p_tenant_id
    AND hc.page_type = p_page_type
    AND hc.is_active = true
    LIMIT 1;
END;
$function$
;]]></string>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
			<input_param name="p_page_type" jt="-1" type="character varying" inOut="1" />
			<input_param name="id" jt="-1" type="uuid" inOut="3" />
			<input_param name="title" jt="-1" type="text" inOut="3" />
			<input_param name="subtitle" jt="-1" type="text" inOut="3" />
			<input_param name="background_color" jt="-1" type="text" inOut="3" />
			<input_param name="text_color" jt="-1" type="text" inOut="3" />
			<input_param name="logo_url" jt="-1" type="text" inOut="3" />
			<input_param name="hero_image_url" jt="-1" type="text" inOut="3" />
			<input_param name="contact_phone" jt="-1" type="character varying" inOut="3" />
			<input_param name="contact_email" jt="-1" type="character varying" inOut="3" />
			<input_param name="portal_instructions" jt="-1" type="text" inOut="3" />
			<input_param name="custom_css" jt="-1" type="text" inOut="3" />
			<input_param name="metadata" jt="-1" type="jsonb" inOut="3" />
		</function>
		<function name="get_complete_guest_portal" id="9b641014-5c87-46a9-9e2a-4d13227ee733" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.get_complete_guest_portal(p_subdomain character varying)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_tenant_id UUID;
    v_result JSONB;
BEGIN
    -- Get tenant ID from subdomain
    SELECT id INTO v_tenant_id
    FROM tenants
    WHERE subdomain = p_subdomain
    AND is_active = true;
    
    IF v_tenant_id IS NULL THEN
        RETURN jsonb_build_object('error', 'Tenant not found');
    END IF;
    
    -- Build complete portal response
    SELECT jsonb_build_object(
        'tenant', jsonb_build_object(
            'id', t.id,
            'name', t.name,
            'branding', COALESCE(t.settings->'portal_branding', '{}')
        ),
        'announcements', (
            SELECT COALESCE(jsonb_agg(
                jsonb_build_object(
                    'id', a.id,
                    'headline', a.headline,
                    'content', a.content,
                    'type', a.announcement_type,
                    'backgroundColor', a.background_color,
                    'textColor', a.text_color,
                    'imageUrl', a.image_url,
                    'actionUrl', a.action_url,
                    'actionText', a.action_text,
                    'metadata', a.metadata
                ) ORDER BY a.display_order
            ), '[]'::jsonb)
            FROM get_active_announcements(t.id) a
        ),
        'requestTypes', (
            SELECT COALESCE(jsonb_agg(
                jsonb_build_object(
                    'id', rt.id,
                    'name', rt.name,
                    'code', rt.code,
                    'department', d.name,
                    'departmentId', d.id,
                    'priority', rt.priority_level,
                    'slaMinutes', rt.sla_minutes
                ) ORDER BY rt.priority_level, rt.name
            ), '[]'::jsonb)
            FROM request_types rt
            JOIN departments d ON rt.department_id = d.id
            WHERE rt.tenant_id = t.id
            AND rt.is_active = true
            AND d.is_active = true
        ),
        'communicationChannels', (
            SELECT COALESCE(jsonb_agg(
                jsonb_build_object(
                    'type', cc.channel_type,
                    'identifier', cc.channel_identifier,
                    'name', cc.channel_name,
                    'isPrimary', cc.is_primary
                ) ORDER BY cc.is_primary DESC, cc.channel_type
            ), '[]'::jsonb)
            FROM communication_channels cc
            WHERE cc.tenant_id = t.id
            AND cc.is_active = true
        )
    ) INTO v_result
    FROM tenants t
    WHERE t.id = v_tenant_id;
    
    RETURN v_result;
END;
$function$
;]]></string>
			<comment><![CDATA[Returns complete guest portal data including tenant branding, active announcements, request types, and communication channels. 
Usage: SELECT get_complete_guest_portal('demo');]]></comment>
			<input_param name="p_subdomain" jt="-1" type="character varying" inOut="1" />
		</function>
		<function name="get_current_tenant_id" id="813a2e15-ed4c-438d-8ed3-4f88a29fe4be" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.get_current_tenant_id()
 RETURNS uuid
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- This will be set by your application when connecting
    RETURN current_setting('app.current_tenant_id', true)::UUID;
EXCEPTION
    WHEN OTHERS THEN
        RETURN NULL;
END;
$function$
;]]></string>
			<result_param jt="-1" />
		</function>
		<function name="get_guest_portal_content" id="dd12bf45-3f1d-4404-a206-fc6e8ada2f65" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.get_guest_portal_content(p_tenant_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'announcements', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', a.id,
                    'headline', a.headline,
                    'content', a.content,
                    'type', a.announcement_type,
                    'backgroundColor', a.background_color,
                    'textColor', a.text_color,
                    'imageUrl', a.image_url,
                    'actionUrl', a.action_url,
                    'actionText', a.action_text,
                    'metadata', a.metadata
                ) ORDER BY a.display_order, a.created_at DESC
            )
            FROM get_active_announcements(p_tenant_id) a
        ),
        'request_types', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', rt.id,
                    'name', rt.name,
                    'code', rt.code,
                    'department', d.name,
                    'priority', rt.priority_level
                ) ORDER BY rt.priority_level, rt.name
            )
            FROM request_types rt
            JOIN departments d ON rt.department_id = d.id
            WHERE rt.tenant_id = p_tenant_id
            AND rt.is_active = true
        )
    ) INTO v_result;
    
    RETURN v_result;
END;
$function$
;]]></string>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
		</function>
		<function name="get_guest_portal_data" id="70c35d7d-96fb-4ac9-bbbc-c5d2ef12811f" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.get_guest_portal_data(p_tenant_id uuid)
 RETURNS jsonb
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_result JSONB;
BEGIN
    SELECT jsonb_build_object(
        'header', (
            SELECT row_to_json(hc.*)
            FROM get_active_header_content(p_tenant_id, 'guest_portal') hc
            LIMIT 1
        ),
        'request_types', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'id', rt.id,
                    'name', rt.name,
                    'code', rt.code,
                    'department', d.name,
                    'priority_level', rt.priority_level
                ) ORDER BY rt.priority_level, rt.name
            )
            FROM request_types rt
            JOIN departments d ON rt.department_id = d.id
            WHERE rt.tenant_id = p_tenant_id
            AND rt.is_active = true
        ),
        'communication_channels', (
            SELECT jsonb_agg(
                jsonb_build_object(
                    'type', cc.channel_type,
                    'identifier', cc.channel_identifier,
                    'is_primary', cc.is_primary
                )
            )
            FROM communication_channels cc
            WHERE cc.tenant_id = p_tenant_id
            AND cc.is_active = true
        )
    ) INTO v_result;
    
    RETURN v_result;
END;
$function$
;]]></string>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
		</function>
		<function name="log_request_status_change" id="9415d51d-09ce-42f9-841a-5b90e072db12" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.log_request_status_change()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        INSERT INTO request_actions (
            request_id,
            action_type,
            action_data
        ) VALUES (
            NEW.id,
            'status_changed',
            jsonb_build_object(
                'old_status', OLD.status,
                'new_status', NEW.status
            )
        );
    END IF;
    
    -- Update timestamp fields based on status
    IF NEW.status = 'assigned' AND OLD.status != 'assigned' THEN
        NEW.assigned_at = NOW();
    ELSIF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    
    RETURN NEW;
END;
$function$
;]]></string>
			<result_param jt="-1" />
		</function>
		<function name="reorder_announcements" id="5a96d179-f7d2-455f-8c85-c013145bf83f" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.reorder_announcements(p_tenant_id uuid, p_announcement_ids uuid[])
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_id UUID;
    v_order INTEGER := 0;
BEGIN
    FOREACH v_id IN ARRAY p_announcement_ids
    LOOP
        UPDATE guest_announcements
        SET display_order = v_order
        WHERE id = v_id 
        AND tenant_id = p_tenant_id;
        
        v_order := v_order + 1;
    END LOOP;
    
    RETURN TRUE;
END;
$function$
;]]></string>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
			<input_param name="p_announcement_ids" jt="-1" type="ARRAY" inOut="1" />
		</function>
		<function name="update_updated_at_column" id="c50d3930-be5d-4f0b-b5c6-ae21aa1e5b97" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.update_updated_at_column()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$function$
;]]></string>
			<result_param jt="-1" />
		</function>
		<function name="upsert_header_content" id="b33aab28-80e2-4a44-b000-34f7026703bb" isSystem="false" params_known="y" >
			<string><![CDATA[CREATE OR REPLACE FUNCTION public.upsert_header_content(p_tenant_id uuid, p_page_type character varying, p_data jsonb)
 RETURNS uuid
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_header_id UUID;
BEGIN
    -- Deactivate existing active headers for this tenant/page_type
    UPDATE header_content
    SET is_active = false
    WHERE tenant_id = p_tenant_id
    AND page_type = p_page_type
    AND is_active = true;
    
    -- Insert new header content
    INSERT INTO header_content (
        tenant_id,
        page_type,
        title,
        subtitle,
        background_color,
        text_color,
        logo_url,
        hero_image_url,
        contact_phone,
        contact_email,
        portal_instructions,
        custom_css,
        metadata,
        is_active
    ) VALUES (
        p_tenant_id,
        p_page_type,
        COALESCE(p_data->>'title', 'Welcome'),
        COALESCE(p_data->>'subtitle', ''),
        COALESCE(p_data->>'background_color', '#1e40af'),
        COALESCE(p_data->>'text_color', '#ffffff'),
        p_data->>'logo_url',
        p_data->>'hero_image_url',
        p_data->>'contact_phone',
        p_data->>'contact_email',
        p_data->>'portal_instructions',
        p_data->>'custom_css',
        COALESCE(p_data->'metadata', '{}')::jsonb,
        true
    ) RETURNING id INTO v_header_id;
    
    RETURN v_header_id;
END;
$function$
;]]></string>
			<input_param name="p_tenant_id" jt="-1" type="uuid" inOut="1" />
			<input_param name="p_page_type" jt="-1" type="character varying" inOut="1" />
			<input_param name="p_data" jt="-1" type="jsonb" inOut="1" />
		</function>
		<trigger name="end_device_sessions_on_login" table="device_sessions" id="16a57adf-6426-49ba-afcd-1bebd94ca410" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER end_device_sessions_on_login AFTER INSERT ON public.device_sessions FOR EACH ROW EXECUTE FUNCTION end_active_device_sessions()]]></string>
		</trigger>
		<trigger name="track_request_status_changes" table="requests" id="356e1cb2-c107-4244-97e9-a4a44bd359a5" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER track_request_status_changes BEFORE UPDATE ON public.requests FOR EACH ROW EXECUTE FUNCTION log_request_status_change()]]></string>
		</trigger>
		<trigger name="update_api_credentials_updated_at" table="api_credentials" id="fa5099d7-3759-4572-b42f-ad723cea4944" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_api_credentials_updated_at BEFORE UPDATE ON public.api_credentials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_devices_updated_at" table="devices" id="7b4006bd-6017-453f-aa1c-92e2cfe5495b" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_devices_updated_at BEFORE UPDATE ON public.devices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_header_content_updated_at" table="guest_announcements" id="204e4d37-70d7-437d-a1ab-333a85acf5a3" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_header_content_updated_at BEFORE UPDATE ON public.guest_announcements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_requests_updated_at" table="requests" id="b52fbe9c-f6e8-405b-8237-5b2e98eef3b2" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_requests_updated_at BEFORE UPDATE ON public.requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_rooms_updated_at" table="rooms" id="ce3a3b83-eb28-48c7-8783-b619329a7be4" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_rooms_updated_at BEFORE UPDATE ON public.rooms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_tenants_updated_at" table="tenants" id="5fbb7b59-9e2c-45ac-b159-8a629afedbe7" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON public.tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_users_updated_at" table="users" id="e1a645a3-bb5e-4224-93ae-b4b96f5796f3" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
		<trigger name="update_workflows_updated_at" table="workflows" id="86fefe7c-f7f1-4425-bf44-2660b65fe5d1" isSystem="false" >
			<string><![CDATA[CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON public.workflows FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()]]></string>
		</trigger>
	</schema>
	<layout name="Main Diagram" id="056a17ae-3431-49c9-84c9-3f98097c4aec" show_relation="columns" >
		<entity schema="postgres.public" name="api_credentials" color="C7F4BE" x="2162" y="1288" />
		<entity schema="postgres.public" name="communication_channels" color="C7F4BE" x="2530" y="1288" />
		<entity schema="postgres.public" name="departments" color="F4DDBE" x="1150" y="2645" />
		<entity schema="postgres.public" name="device_departments" color="BEBEF4" x="368" y="506" />
		<entity schema="postgres.public" name="device_sessions" color="BEBEF4" x="690" y="506" />
		<entity schema="postgres.public" name="devices" color="BEBEF4" x="690" y="1150" />
		<entity schema="postgres.public" name="guest_announcements" color="C7F4BE" x="2162" y="1679" />
		<entity schema="postgres.public" name="message_logs" color="F4DDBE" x="1472" y="2208" />
		<entity schema="postgres.public" name="message_templates" color="F4DDBE" x="1150" y="2208" />
		<entity schema="postgres.public" name="request_actions" color="BEBEF4" x="69" y="391" />
		<entity schema="postgres.public" name="request_assignments" color="F4DDBE" x="805" y="2277" />
		<entity schema="postgres.public" name="request_attachments" color="BEBEF4" x="322" y="759" />
		<entity schema="postgres.public" name="request_types" color="F4DDBE" x="828" y="2645" />
		<entity schema="postgres.public" name="requests" color="F4DDBE" x="805" y="1610" />
		<entity schema="postgres.public" name="rooms" color="C7F4BE" x="1840" y="1679" />
		<entity schema="postgres.public" name="tenants" color="C7F4BE" x="2162" y="897" />
		<entity schema="postgres.public" name="users" color="BEBEF4" x="368" y="115" />
		<entity schema="postgres.public" name="workflows" color="C7F4BE" x="1840" y="1265" />
		<entity schema="postgres.public" name="active_guest_announcements" color="BED3F4" x="1035" y="897" />
		<entity schema="postgres.public" name="announcement_management" color="BED3F4" x="1426" y="897" />
		<group name="active_guest_announcements" color="ECF0F7" >
			<entity schema="postgres.public" name="active_guest_announcements" />
			<entity schema="postgres.public" name="announcement_management" />
		</group>
		<group name="tenants" color="EEF7EC" >
			<entity schema="postgres.public" name="tenants" />
			<entity schema="postgres.public" name="api_credentials" />
			<entity schema="postgres.public" name="communication_channels" />
			<entity schema="postgres.public" name="guest_announcements" />
			<entity schema="postgres.public" name="rooms" />
			<entity schema="postgres.public" name="workflows" />
		</group>
		<group name="message_templates" color="F7F2EC" >
			<entity schema="postgres.public" name="message_templates" />
			<entity schema="postgres.public" name="departments" />
			<entity schema="postgres.public" name="message_logs" />
			<entity schema="postgres.public" name="request_types" />
			<entity schema="postgres.public" name="requests" />
			<entity schema="postgres.public" name="request_assignments" />
		</group>
		<group name="devices" color="ECECF7" >
			<entity schema="postgres.public" name="devices" />
			<entity schema="postgres.public" name="users" />
			<entity schema="postgres.public" name="device_departments" />
			<entity schema="postgres.public" name="device_sessions" />
			<entity schema="postgres.public" name="request_attachments" />
			<entity schema="postgres.public" name="request_actions" />
		</group>
	</layout>
	<layout name="~Diagram with Sample Tools" id="89b5558e-55c2-4b04-a3ef-4e0f9d982ce1" show_column_type="y" show_relation="columns" >
		<entity schema="postgres.public" name="api_credentials" color="C7F4BE" x="2507" y="1265" />
		<entity schema="postgres.public" name="communication_channels" color="C7F4BE" x="2898" y="1265" />
		<entity schema="postgres.public" name="departments" color="F4DDBE" x="1311" y="2599" />
		<entity schema="postgres.public" name="device_departments" color="BEBEF4" x="437" y="460" />
		<entity schema="postgres.public" name="device_sessions" color="BEBEF4" x="782" y="460" />
		<entity schema="postgres.public" name="devices" color="BEBEF4" x="782" y="1104" />
		<entity schema="postgres.public" name="guest_announcements" color="C7F4BE" x="2484" y="1656" />
		<entity schema="postgres.public" name="message_logs" color="F4DDBE" x="1702" y="2162" />
		<entity schema="postgres.public" name="message_templates" color="F4DDBE" x="1311" y="2162" />
		<entity schema="postgres.public" name="request_actions" color="BEBEF4" x="69" y="345" />
		<entity schema="postgres.public" name="request_assignments" color="F4DDBE" x="920" y="2231" />
		<entity schema="postgres.public" name="request_attachments" color="BEBEF4" x="391" y="713" />
		<entity schema="postgres.public" name="request_types" color="F4DDBE" x="966" y="2599" />
		<entity schema="postgres.public" name="requests" color="F4DDBE" x="920" y="1564" />
		<entity schema="postgres.public" name="rooms" color="C7F4BE" x="2116" y="1196" />
		<entity schema="postgres.public" name="tenants" color="C7F4BE" x="2898" y="874" />
		<entity schema="postgres.public" name="users" color="BEBEF4" x="437" y="69" />
		<entity schema="postgres.public" name="workflows" color="C7F4BE" x="2507" y="851" />
		<entity schema="postgres.public" name="active_guest_announcements" color="BED3F4" x="1219" y="851" />
		<entity schema="postgres.public" name="announcement_management" color="BED3F4" x="1656" y="851" />
		<script name="SQL Editor" id="0cc2536d-7c72-40db-975b-03c69257b4be" language="SQL" >
			<string><![CDATA[SELECT
	id, name, subdomain, subscription_tier, is_active, settings, created_at, updated_at
FROM
	"public".tenants s;]]></string>
		</script>
		<browser id="3815ec2f-f721-419f-a73b-f562c7acee3f" name="Relational Data Editor" confirm_updates="y" >
			<browse_table schema="postgres.public" entity="tenants" fk_out="n" x="20" y="20" width="270" height="-60" >
				<browse_table schema="postgres.public" entity="departments" fk="departments_tenant_id_fkey" fk_out="y" x="310" y="20" width="500" height="350" >
					<browse_table schema="postgres.public" entity="device_departments" fk="device_departments_department_id_fkey" fk_out="y" x="830" y="20" width="500" height="350" />
				</browse_table>
			</browse_table>
		</browser>
		<query id="59d1c80a-5830-4362-8eab-03334adb6f4c" name="Query Builder" >
			<query_table schema="postgres.public" name="tenants" alias="t" x="69" y="69" >
				<column name="id" />
				<column name="name" />
				<column name="subdomain" />
				<column name="subscription_tier" />
				<column name="is_active" />
				<column name="settings" />
				<column name="created_at" />
				<column name="updated_at" />
				<query_table schema="postgres.public" name="departments" alias="d" x="345" y="69" fk="departments_tenant_id_fkey" type="Inner Join" >
					<column name="id" />
					<column name="tenant_id" />
					<column name="name" />
					<column name="code" />
					<column name="is_active" />
					<column name="created_at" />
					<query_table schema="postgres.public" name="device_departments" alias="dd" x="575" y="69" fk="device_departments_department_id_fkey" type="Inner Join" >
						<column name="device_id" />
						<column name="department_id" />
					</query_table>
				</query_table>
			</query_table>
		</query>
	</layout>
</project>