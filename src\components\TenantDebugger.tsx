import React from 'react';
import { getCurrentTenant } from '../lib/tenant-config';

const TenantDebugger: React.FC = () => {
  // Only show in development
  if (import.meta.env.PROD) {
    return null;
  }

  const tenant = getCurrentTenant();

  return (
    <div className="fixed bottom-4 right-4 bg-gray-900 text-white p-4 rounded-lg shadow-lg max-w-sm z-50">
      <h3 className="text-sm font-bold mb-2">Tenant Debug Info</h3>
      <div className="text-xs space-y-1">
        <div><span className="text-gray-400">ID:</span> {tenant.id}</div>
        <div><span className="text-gray-400">Name:</span> {tenant.name}</div>
        <div><span className="text-gray-400">Subdomain:</span> {tenant.subdomain}</div>
        {tenant.customDomain && (
          <div><span className="text-gray-400">Custom Domain:</span> {tenant.customDomain}</div>
        )}
        <div className="mt-2 pt-2 border-t border-gray-700">
          <p className="text-gray-400">Test other tenants:</p>
          <div className="space-x-2 mt-1">
            <a href="?tenant=demo" className="text-blue-400 hover:text-blue-300">Demo</a>
            <a href="?tenant=parkhotel" className="text-blue-400 hover:text-blue-300">Park Hotel</a>
            <a href="?tenant=oceanview" className="text-blue-400 hover:text-blue-300">Ocean View</a>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TenantDebugger;