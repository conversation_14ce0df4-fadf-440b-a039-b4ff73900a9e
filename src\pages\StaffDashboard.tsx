import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { ArrowLeft, Settings } from 'lucide-react';
import StaffLoginForm from '../components/staff/StaffLoginForm';
import StaffStats from '../components/staff/StaffStats';
import WorkOrderQueue, { WorkOrder } from '../components/staff/WorkOrderQueue';
import RoomStatusBoard from '../components/staff/RoomStatusBoard';
import LiveActivityFeed from '../components/staff/LiveActivityFeed';
import { getCurrentTenant } from '../lib/tenant-config';

const StaffDashboard: React.FC = () => {
  const tenant = getCurrentTenant();
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [workOrders, setWorkOrders] = useState<WorkOrder[]>([]);
  const [roomStatuses] = useState(
    tenant.settings?.demoConfig?.demoRooms || {
      '101': 'Clean',
      '102': 'Occupied',
      '103': 'Maintenance',
      '104': 'Clean',
      '105': 'Occupied',
      '106': 'Housekeeping'
    }
  );

  useEffect(() => {
    if (isLoggedIn) {
      // Load work orders from localStorage
      const requests = JSON.parse(localStorage.getItem('guestRequests') || '[]');
      setWorkOrders(requests);

      // Set up interval to check for new requests
      const interval = setInterval(() => {
        const updatedRequests = JSON.parse(localStorage.getItem('guestRequests') || '[]');
        setWorkOrders(updatedRequests);
      }, 2000);

      return () => clearInterval(interval);
    }
  }, [isLoggedIn]);

  const sendWebhookNotification = async (order: WorkOrder, newStatus: string, assignedTo?: string) => {
    try {
      const webhookData = {
        workOrderId: order.id,
        room: order.room,
        service: order.service,
        type: order.type,
        description: order.description,
        urgency: order.urgency,
        previousStatus: order.status,
        newStatus: newStatus,
        assignedTo: assignedTo || order.assignedTo,
        timestamp: new Date().toISOString(),
        staffMember: 'John Doe' // In real app, this would come from auth
      };

      const response = await fetch('https://n8n.hospitalityflows.com/webhook/staff-update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(webhookData)
      });

      if (!response.ok) {
        console.error('Webhook notification failed:', response.statusText);
      }
    } catch (error) {
      console.error('Error sending webhook notification:', error);
    }
  };

  const updateWorkOrderStatus = async (id: number, status: WorkOrder['status'], assignedTo?: string) => {
    const orderToUpdate = workOrders.find(order => order.id === id);
    if (!orderToUpdate) return;

    // Send webhook notification before updating status
    await sendWebhookNotification(orderToUpdate, status, assignedTo);

    const updatedOrders = workOrders.map(order => 
      order.id === id ? { ...order, status, assignedTo } : order
    );
    setWorkOrders(updatedOrders);
    localStorage.setItem('guestRequests', JSON.stringify(updatedOrders));
  };

  // Calculate stats
  const stats = {
    pendingCount: workOrders.filter(order => order.status === 'Pending').length,
    completedCount: workOrders.filter(order => order.status === 'Complete').length
  };

  if (!isLoggedIn) {
    return <StaffLoginForm onLogin={() => setIsLoggedIn(true)} />;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="bg-blue-900 text-white py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center">
            <div>
              <Link to="/" className="flex items-center gap-2 text-amber-400 hover:text-amber-300 mb-2 transition-colors">
                <ArrowLeft className="w-4 h-4" />
                Back to Hotel
              </Link>
              <h1 className="text-2xl font-bold">Staff Dashboard</h1>
            </div>
            <div className="flex items-center gap-4">
              <Link
                to="/admin"
                className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                <Settings className="w-4 h-4" />
                Admin Panel
              </Link>
              <button
                onClick={() => setIsLoggedIn(false)}
                className="bg-red-600 hover:bg-red-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <StaffStats stats={stats} />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2">
            <WorkOrderQueue 
              workOrders={workOrders} 
              onUpdateStatus={updateWorkOrderStatus} 
            />
          </div>

          <div>
            <RoomStatusBoard roomStatuses={roomStatuses} />
            <LiveActivityFeed />
          </div>
        </div>
      </div>
    </div>
  );
};

export default StaffDashboard;