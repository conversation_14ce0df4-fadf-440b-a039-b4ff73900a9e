import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { Plus, Palette, Eye, Edit3, Trash2, Save, X, ExternalLink, Clock, ChevronUp, ChevronDown } from 'lucide-react';
import { GuestAnnouncement } from '../../lib/supabase';
import { getCurrentTenant } from '../../lib/tenant-config';

export interface AnnouncementForm {
  headline: string;
  content: string;
  announcement_type: string;
  background_color: string;
  text_color: string;
  start_date: string;
  end_date: string | null;
  image_url: string | null;
  action_url: string | null;
  action_text: string | null;
}

interface AnnouncementManagementProps {
  announcements: GuestAnnouncement[];
  onSave: (form: AnnouncementForm, editingId?: string) => void;
  onDelete: (id: string) => void;
  onToggleActive: (id: string, isActive: boolean) => void;
  onReorder: (id: string, direction: 'up' | 'down') => void;
}

const announcementTypes = [
  { value: 'general', label: 'General', defaultColor: '#3b82f6' },
  { value: 'dining', label: 'Dining', defaultColor: '#059669' },
  { value: 'event', label: 'Event', defaultColor: '#1e3a8a' },
  { value: 'promotion', label: 'Promotion', defaultColor: '#7c3aed' },
  { value: 'alert', label: 'Alert', defaultColor: '#dc2626' }
];

const AnnouncementManagement: React.FC<AnnouncementManagementProps> = ({
  announcements,
  onSave,
  onDelete,
  onToggleActive,
  onReorder
}) => {
  const tenant = getCurrentTenant();
  const featuredRoom = tenant.settings?.demoConfig?.featuredRoom || '101';
  const [isAddingAnnouncement, setIsAddingAnnouncement] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<GuestAnnouncement | null>(null);
  const [announcementForm, setAnnouncementForm] = useState<AnnouncementForm>({
    headline: '',
    content: '',
    announcement_type: 'general',
    background_color: '#3b82f6',
    text_color: '#ffffff',
    start_date: new Date().toISOString().slice(0, 16),
    end_date: null,
    image_url: null,
    action_url: null,
    action_text: null
  });

  const getAnnouncementIcon = (type: string) => {
    switch (type) {
      case 'dining': return '🍽️';
      case 'event': return '🎉';
      case 'promotion': return '🎁';
      case 'alert': return '⚠️';
      default: return '📢';
    }
  };

  const isAnnouncementActive = (announcement: GuestAnnouncement) => {
    if (!announcement.is_active) return false;
    
    const now = new Date();
    const startDate = new Date(announcement.start_date);
    const endDate = announcement.end_date ? new Date(announcement.end_date) : null;
    
    return now >= startDate && (!endDate || now <= endDate);
  };

  const handleTypeChange = (type: string) => {
    const selectedType = announcementTypes.find(t => t.value === type);
    setAnnouncementForm({
      ...announcementForm,
      announcement_type: type,
      background_color: selectedType?.defaultColor || '#3b82f6'
    });
  };

  const startEditingAnnouncement = (announcement: GuestAnnouncement) => {
    setEditingAnnouncement(announcement);
    setAnnouncementForm({
      headline: announcement.headline,
      content: announcement.content,
      announcement_type: announcement.announcement_type,
      background_color: announcement.background_color,
      text_color: announcement.text_color,
      start_date: announcement.start_date ? new Date(announcement.start_date).toISOString().slice(0, 16) : '',
      end_date: announcement.end_date ? new Date(announcement.end_date).toISOString().slice(0, 16) : null,
      image_url: announcement.image_url,
      action_url: announcement.action_url,
      action_text: announcement.action_text
    });
    setIsAddingAnnouncement(true);
  };

  const handleSave = () => {
    onSave(announcementForm, editingAnnouncement?.id);
    // Reset form
    setAnnouncementForm({
      headline: '',
      content: '',
      announcement_type: 'general',
      background_color: '#3b82f6',
      text_color: '#ffffff',
      start_date: new Date().toISOString().slice(0, 16),
      end_date: null,
      image_url: null,
      action_url: null,
      action_text: null
    });
    setIsAddingAnnouncement(false);
    setEditingAnnouncement(null);
  };

  return (
    <div className="bg-white rounded-lg shadow-lg mb-8">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-3">
            <Palette className="w-6 h-6 text-purple-600" />
            <h2 className="text-xl font-bold text-gray-900">Guest Announcements</h2>
          </div>
          <div className="flex gap-2">
            <Link
              to={`/room/${featuredRoom}`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded-lg text-sm font-medium transition-colors"
            >
              <Eye className="w-4 h-4" />
              Preview
            </Link>
            <button
              onClick={() => {
                setIsAddingAnnouncement(true);
                setEditingAnnouncement(null);
                setAnnouncementForm({
                  headline: '',
                  content: '',
                  announcement_type: 'general',
                  background_color: '#3b82f6',
                  text_color: '#ffffff',
                  start_date: new Date().toISOString().slice(0, 16),
                  end_date: null,
                  image_url: null,
                  action_url: null,
                  action_text: null
                });
              }}
              className="flex items-center gap-2 bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
            >
              <Plus className="w-4 h-4" />
              Add Announcement
            </button>
          </div>
        </div>
      </div>
      
      <div className="p-6">
        {/* Add/Edit Announcement Form */}
        {isAddingAnnouncement && (
          <div className="mb-6 p-6 bg-purple-50 rounded-lg border border-purple-200">
            <h3 className="text-lg font-bold text-gray-900 mb-4">
              {editingAnnouncement ? 'Edit Announcement' : 'New Announcement'}
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Announcement Type
                </label>
                <select
                  value={announcementForm.announcement_type}
                  onChange={(e) => handleTypeChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {announcementTypes.map(type => (
                    <option key={type.value} value={type.value}>{type.label}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Headline
                </label>
                <input
                  type="text"
                  value={announcementForm.headline}
                  onChange={(e) => setAnnouncementForm({ ...announcementForm, headline: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Tonight's Dinner Special"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Content
                </label>
                <textarea
                  value={announcementForm.content}
                  onChange={(e) => setAnnouncementForm({ ...announcementForm, content: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="Fresh Atlantic Salmon with herb butter..."
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date & Time
                  </label>
                  <input
                    type="datetime-local"
                    value={announcementForm.start_date}
                    onChange={(e) => setAnnouncementForm({ ...announcementForm, start_date: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date & Time (Optional)
                  </label>
                  <input
                    type="datetime-local"
                    value={announcementForm.end_date || ''}
                    onChange={(e) => setAnnouncementForm({ ...announcementForm, end_date: e.target.value || null })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Background Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={announcementForm.background_color}
                      onChange={(e) => setAnnouncementForm({ ...announcementForm, background_color: e.target.value })}
                      className="w-12 h-10 border border-gray-300 rounded-md cursor-pointer"
                    />
                    <input
                      type="text"
                      value={announcementForm.background_color}
                      onChange={(e) => setAnnouncementForm({ ...announcementForm, background_color: e.target.value })}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"
                    />
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Text Color
                  </label>
                  <div className="flex items-center gap-3">
                    <input
                      type="color"
                      value={announcementForm.text_color}
                      onChange={(e) => setAnnouncementForm({ ...announcementForm, text_color: e.target.value })}
                      className="w-12 h-10 border border-gray-300 rounded-md cursor-pointer"
                    />
                    <input
                      type="text"
                      value={announcementForm.text_color}
                      onChange={(e) => setAnnouncementForm({ ...announcementForm, text_color: e.target.value })}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 font-mono text-sm"
                    />
                  </div>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Action Button (Optional)
                </label>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <input
                    type="text"
                    value={announcementForm.action_text || ''}
                    onChange={(e) => setAnnouncementForm({ ...announcementForm, action_text: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="Button Text (e.g., Reserve Now)"
                  />
                  <input
                    type="url"
                    value={announcementForm.action_url || ''}
                    onChange={(e) => setAnnouncementForm({ ...announcementForm, action_url: e.target.value })}
                    className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                    placeholder="https://..."
                  />
                </div>
              </div>
              
              {/* Live Preview */}
              <div className="border-2 border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-gray-100 px-4 py-2 text-sm font-medium text-gray-700 border-b border-gray-200">
                  Live Preview
                </div>
                <div 
                  className="p-6"
                  style={{ 
                    backgroundColor: announcementForm.background_color,
                    color: announcementForm.text_color 
                  }}
                >
                  <div className="flex items-start gap-3">
                    <span className="text-2xl">{getAnnouncementIcon(announcementForm.announcement_type || 'general')}</span>
                    <div className="flex-1">
                      <h3 className="text-xl font-bold mb-2">{announcementForm.headline || 'Announcement Headline'}</h3>
                      <p className="opacity-90 mb-3">{announcementForm.content || 'Announcement content will appear here...'}</p>
                      {announcementForm.action_text && (
                        <button className="px-4 py-2 bg-white/20 hover:bg-white/30 rounded-lg font-medium transition-colors">
                          {announcementForm.action_text}
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <button
                  onClick={handleSave}
                  className="flex items-center gap-2 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  <Save className="w-4 h-4" />
                  {editingAnnouncement ? 'Update' : 'Save'} Announcement
                </button>
                <button
                  onClick={() => {
                    setIsAddingAnnouncement(false);
                    setEditingAnnouncement(null);
                  }}
                  className="flex items-center gap-2 bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                >
                  <X className="w-4 h-4" />
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Announcements List */}
        <div className="space-y-4">
          {announcements.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No announcements configured yet.</p>
              <p className="text-sm mt-2">Click "Add Announcement" to create content for your guest services page.</p>
            </div>
          ) : (
            announcements.map((announcement, index) => (
              <div
                key={announcement.id}
                className={`border rounded-lg overflow-hidden ${
                  isAnnouncementActive(announcement) ? 'border-gray-200' : 'border-gray-200 opacity-60'
                }`}
              >
                <div 
                  className="p-4"
                  style={{ 
                    backgroundColor: announcement.background_color,
                    color: announcement.text_color 
                  }}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <span className="text-xl">{getAnnouncementIcon(announcement.announcement_type)}</span>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-bold">{announcement.headline}</h3>
                          <span className="text-xs opacity-75 bg-white/20 px-2 py-1 rounded">
                            {announcement.announcement_type}
                          </span>
                        </div>
                        <p className="text-sm opacity-90">{announcement.content}</p>
                        {announcement.action_text && (
                          <div className="mt-2 flex items-center gap-2 text-xs">
                            <ExternalLink className="w-3 h-3" />
                            <span>{announcement.action_text}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <button
                        onClick={() => onReorder(announcement.id, 'up')}
                        disabled={index === 0}
                        className={`p-1 ${index === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/20'} rounded`}
                      >
                        <ChevronUp className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => onReorder(announcement.id, 'down')}
                        disabled={index === announcements.length - 1}
                        className={`p-1 ${index === announcements.length - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-white/20'} rounded`}
                      >
                        <ChevronDown className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
                <div className="bg-gray-50 px-4 py-3 flex items-center justify-between">
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <div className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      <span>
                        {new Date(announcement.start_date).toLocaleString()} 
                        {announcement.end_date && ` - ${new Date(announcement.end_date).toLocaleString()}`}
                      </span>
                    </div>
                    <button
                      onClick={() => onToggleActive(announcement.id, announcement.is_active)}
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        announcement.is_active 
                          ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                          : 'bg-red-100 text-red-800 hover:bg-red-200'
                      } transition-colors`}
                    >
                      {announcement.is_active ? 'Active' : 'Inactive'}
                    </button>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => startEditingAnnouncement(announcement)}
                      className="text-blue-600 hover:text-blue-800 p-1"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => onDelete(announcement.id)}
                      className="text-red-600 hover:text-red-800 p-1"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default AnnouncementManagement;