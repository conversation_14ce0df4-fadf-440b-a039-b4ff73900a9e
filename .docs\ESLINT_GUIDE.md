# ESLint Configuration Guide

## Added Rules

Your ESLint configuration now includes the following custom rules:

### 1. `@typescript-eslint/no-unused-vars: ["warn", { "argsIgnorePattern": "^_|^e$|^event$" }]`
- **Purpose**: Warns about unused variables
- **Exceptions**: Ignores variables that:
  - Start with underscore (`_`)
  - Are exactly `e` (common for event handlers)
  - Are exactly `event` (common for event handlers)
- **Example**: 
  ```typescript
  // No warning:
  const handleClick = (e: MouseEvent) => { /* ... */ }
  const _unusedVar = "This is intentionally unused";
  
  // Warning:
  const unusedVar = "This will trigger a warning";
  ```

### 2. `no-undef: "warn"`
- **Purpose**: Warns when using undefined variables
- **Note**: In TypeScript projects, this is often caught by TypeScript itself
- **Example**:
  ```typescript
  // Warning:
  console.log(undefinedVariable); // 'undefinedVariable' is not defined
  ```

### 3. `react/jsx-no-undef: "warn"`
- **Purpose**: Warns when using undefined JSX components
- **Example**:
  ```tsx
  // Warning if MyComponent is not imported:
  return <MyComponent />;
  ```

### 4. `react-hooks/exhaustive-deps: "warn"`
- **Purpose**: Ensures useEffect dependencies are correctly specified
- **Example**:
  ```typescript
  // Warning:
  useEffect(() => {
    fetchData();
  }, []); // Missing 'fetchData' dependency
  ```

### 5. `no-self-assign: "error"`
- **Purpose**: Prevents self-assignment (code error)
- **Example**:
  ```typescript
  // Error:
  x = x; // Self-assignment
  ```

## Running Lint

To check your code:
```bash
npm run lint
```

To auto-fix some issues:
```bash
npm run lint -- --fix
```

## Current Warnings

Based on the lint output, you have some warnings to address:
1. Missing `process` global in TenantDebugger.tsx
2. Unused variables in AdminPanel.tsx and StaffDashboard.tsx
3. Missing useEffect dependencies in several files

## Fixing Common Issues

### For `process is not defined`:
```typescript
// Add at the top of your vite-env.d.ts:
declare const process: {
  env: {
    NODE_ENV: string
  }
}
```

### For unused variables:
```typescript
// Prefix with underscore:
const _setDepartments = useState([])[1];
// Or remove if truly not needed
```

### For useEffect dependencies:
```typescript
// Option 1: Add the dependency
useEffect(() => {
  fetchData();
}, [fetchData]);

// Option 2: If function doesn't change, define inside useEffect
useEffect(() => {
  const fetchData = async () => { /* ... */ };
  fetchData();
}, []);