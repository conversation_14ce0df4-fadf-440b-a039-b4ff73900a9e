import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Hotel, Users } from 'lucide-react';

const Navigation: React.FC = () => {
  const location = useLocation();

  const navItems = [
    { name: 'Home', path: '/' },
    { name: 'Rooms', path: '/rooms' },
    { name: 'Services', path: '/services' },
    { name: 'Contact', path: '/contact' },
  ];

  return (
    <nav className="bg-blue-900 text-white shadow-lg">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center gap-2 font-bold text-xl">
            <Hotel className="w-8 h-8 text-amber-400" />
            <span>Riverside Inn</span>
          </Link>

          <div className="hidden md:flex items-center space-x-8">
            {navItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  location.pathname === item.path
                    ? 'bg-blue-800 text-amber-400'
                    : 'text-white hover:bg-blue-800 hover:text-amber-400'
                }`}
              >
                {item.name}
              </Link>
            ))}
            <Link
              to="/staff"
              className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700 px-4 py-2 rounded-md text-sm font-medium transition-colors"
            >
              <Users className="w-4 h-4" />
              Staff Portal
            </Link>
          </div>

          <div className="md:hidden">
            <Link
              to="/staff"
              className="flex items-center gap-2 bg-amber-600 hover:bg-amber-700 px-3 py-2 rounded-md text-sm font-medium transition-colors"
            >
              <Users className="w-4 h-4" />
              Staff
            </Link>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;