# Demo to Production Migration Guide

This document outlines the changes made to remove hardcoded "room237" references and make the system production-ready.

## Summary of Changes

### 1. **Dynamic Room Routes**
- Changed route from `/room237` to `/room/:roomNumber` 
- All room numbers are now dynamic URL parameters
- Guest services page reads room number from URL

### 2. **Tenant-Based Demo Configuration**
The demo tenant now has specific configuration in `tenant-config.ts`:
```typescript
demoConfig: {
  featuredRoom: '237',
  demoRooms: {
    '235': 'Clean',
    '236': 'Occupied', 
    '237': 'Maintenance',
    '238': 'Clean',
    '239': 'Occupied',
    '240': 'Housekeeping'
  }
}
```

### 3. **Files Updated**
- `src/App.tsx` - Dynamic route parameter
- `src/pages/GuestServicesPage.tsx` - Reads room from URL params
- `src/pages/HomePage.tsx` - Uses tenant's featured room
- `src/pages/StaffDashboard.tsx` - Uses tenant's demo rooms
- `src/pages/DemoShowcase.tsx` - Uses tenant's featured room
- `src/components/admin/AnnouncementManagement.tsx` - Dynamic room link
- `src/components/staff/LiveActivityFeed.tsx` - Dynamic room numbers
- `src/lib/tenant-config.ts` - Added demoConfig to tenant interface

## How It Works Now

### For Demo Tenant (demo.hospitalityflows.com)
- Featured room is 237 (configurable)
- Demo rooms 235-240 with preset statuses
- All demo-specific data is in the tenant config

### For Production Tenants
- No demoConfig needed
- Real room numbers from their property
- No hardcoded values

## Adding New Hotels

1. Add tenant to `tenant-config.ts` without demoConfig
2. Hotels use real room numbers in URLs: `/room/101`, `/room/A12`, etc.
3. QR codes generated per room point to: `https://[hotel].hospitalityflows.com/room/[roomNumber]`

## n8n Webhook Updates

Webhooks now receive dynamic room numbers:
```json
{
  "tenant_id": "hotel-uuid",
  "tenant_subdomain": "parkhotel",
  "room": "237",  // Dynamic from URL
  "request": { ... }
}
```

## Benefits

1. **No Mode Switching** - Demo is just another tenant
2. **Clean URLs** - `/room/237` instead of `/room237`
3. **Flexible** - Any room number format works (101, A12, PH-1, etc.)
4. **Scalable** - Easy to add new hotels without code changes

## Testing

- Demo: `https://demo.hospitalityflows.com/room/237`
- Production: `https://parkhotel.hospitalityflows.com/room/101`
- Local: `http://localhost:3000/room/237?tenant=demo`
