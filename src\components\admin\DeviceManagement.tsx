import React, { useState } from 'react';
import { Plus, Phone, Edit3, Trash2, Save, X } from 'lucide-react';

export interface Device {
  id: number;
  name: string;
  phoneNumber: string;
  department: string;
  type: 'mobile' | 'desk' | 'pager';
  isActive: boolean;
}

export interface Department {
  id: number;
  name: string;
  color: string;
}

interface DeviceManagementProps {
  devices: Device[];
  departments: Department[];
  onUpdateDevice: (device: Device) => void;
  onAddDevice: (device: Omit<Device, 'id'>) => void;
  onDeleteDevice: (id: number) => void;
  onToggleActive: (id: number) => void;
}

const DeviceManagement: React.FC<DeviceManagementProps> = ({
  devices,
  departments,
  onUpdateDevice,
  onAddDevice,
  onDeleteDevice,
  onToggleActive
}) => {
  const [editingDevice, setEditingDevice] = useState<Device | null>(null);
  const [isAddingDevice, setIsAddingDevice] = useState(false);
  const [newDevice, setNewDevice] = useState<Omit<Device, 'id'>>({
    name: '',
    phoneNumber: '',
    department: '',
    type: 'mobile',
    isActive: true
  });

  const getDepartmentColor = (departmentName: string) => {
    const dept = departments.find(d => d.name === departmentName);
    return dept ? dept.color : 'bg-gray-500';
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'mobile': return '📱';
      case 'desk': return '☎️';
      case 'pager': return '📟';
      default: return '📞';
    }
  };

  const handleSaveDevice = () => {
    if (editingDevice) {
      onUpdateDevice(editingDevice);
      setEditingDevice(null);
    }
  };

  const handleAddDevice = () => {
    if (newDevice.name && newDevice.phoneNumber && newDevice.department) {
      onAddDevice(newDevice);
      setNewDevice({
        name: '',
        phoneNumber: '',
        department: '',
        type: 'mobile',
        isActive: true
      });
      setIsAddingDevice(false);
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-lg">
      <div className="p-6 border-b border-gray-200">
        <div className="flex justify-between items-center">
          <h2 className="text-xl font-bold text-gray-900">Device Management</h2>
          <button
            onClick={() => setIsAddingDevice(true)}
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
          >
            <Plus className="w-4 h-4" />
            Add Device
          </button>
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone Number</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {/* Add New Device Row */}
            {isAddingDevice && (
              <tr className="bg-blue-50">
                <td className="px-6 py-4">
                  <input
                    type="text"
                    value={newDevice.name}
                    onChange={(e) => setNewDevice({ ...newDevice, name: e.target.value })}
                    placeholder="Device name"
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  />
                </td>
                <td className="px-6 py-4">
                  <input
                    type="tel"
                    value={newDevice.phoneNumber}
                    onChange={(e) => setNewDevice({ ...newDevice, phoneNumber: e.target.value })}
                    placeholder="+****************"
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  />
                </td>
                <td className="px-6 py-4">
                  <select
                    value={newDevice.department}
                    onChange={(e) => setNewDevice({ ...newDevice, department: e.target.value })}
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="">Select department</option>
                    {departments.map(dept => (
                      <option key={dept.id} value={dept.name}>{dept.name}</option>
                    ))}
                  </select>
                </td>
                <td className="px-6 py-4">
                  <select
                    value={newDevice.type}
                    onChange={(e) => setNewDevice({ ...newDevice, type: e.target.value as 'mobile' | 'desk' | 'pager' })}
                    className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                  >
                    <option value="mobile">Mobile</option>
                    <option value="desk">Desk Phone</option>
                    <option value="pager">Pager</option>
                  </select>
                </td>
                <td className="px-6 py-4">
                  <div className="flex gap-2">
                    <button
                      onClick={handleAddDevice}
                      className="text-green-600 hover:text-green-800 p-1"
                    >
                      <Save className="w-4 h-4" />
                    </button>
                    <button
                      onClick={() => setIsAddingDevice(false)}
                      className="text-red-600 hover:text-red-800 p-1"
                    >
                      <X className="w-4 h-4" />
                    </button>
                  </div>
                </td>
              </tr>
            )}

            {/* Existing Devices */}
            {devices.map((device) => (
              <tr key={device.id} className={device.isActive ? '' : 'bg-gray-50 opacity-60'}>
                <td className="px-6 py-4">
                  {editingDevice?.id === device.id ? (
                    <input
                      type="text"
                      value={editingDevice.name}
                      onChange={(e) => setEditingDevice({ ...editingDevice, name: e.target.value })}
                      className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{getTypeIcon(device.type)}</span>
                      <div>
                        <div className="font-medium text-gray-900">{device.name}</div>
                        <div className="text-sm text-gray-500 capitalize">{device.type}</div>
                      </div>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4">
                  {editingDevice?.id === device.id ? (
                    <input
                      type="tel"
                      value={editingDevice.phoneNumber}
                      onChange={(e) => setEditingDevice({ ...editingDevice, phoneNumber: e.target.value })}
                      className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span className="font-mono text-sm">{device.phoneNumber}</span>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4">
                  {editingDevice?.id === device.id ? (
                    <select
                      value={editingDevice.department}
                      onChange={(e) => setEditingDevice({ ...editingDevice, department: e.target.value })}
                      className="w-full px-3 py-1 border border-gray-300 rounded-md text-sm"
                    >
                      {departments.map(dept => (
                        <option key={dept.id} value={dept.name}>{dept.name}</option>
                      ))}
                    </select>
                  ) : (
                    <div className="flex items-center gap-2">
                      <div className={`w-3 h-3 rounded-full ${getDepartmentColor(device.department)}`}></div>
                      <span className="text-sm font-medium">{device.department}</span>
                    </div>
                  )}
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={() => onToggleActive(device.id)}
                    className={`px-3 py-1 rounded-full text-xs font-medium ${
                      device.isActive 
                        ? 'bg-green-100 text-green-800 hover:bg-green-200' 
                        : 'bg-red-100 text-red-800 hover:bg-red-200'
                    } transition-colors`}
                  >
                    {device.isActive ? 'Active' : 'Inactive'}
                  </button>
                </td>
                <td className="px-6 py-4">
                  <div className="flex gap-2">
                    {editingDevice?.id === device.id ? (
                      <>
                        <button
                          onClick={handleSaveDevice}
                          className="text-green-600 hover:text-green-800 p-1"
                        >
                          <Save className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => setEditingDevice(null)}
                          className="text-red-600 hover:text-red-800 p-1"
                        >
                          <X className="w-4 h-4" />
                        </button>
                      </>
                    ) : (
                      <>
                        <button
                          onClick={() => setEditingDevice(device)}
                          className="text-blue-600 hover:text-blue-800 p-1"
                        >
                          <Edit3 className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => onDeleteDevice(device.id)}
                          className="text-red-600 hover:text-red-800 p-1"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default DeviceManagement;